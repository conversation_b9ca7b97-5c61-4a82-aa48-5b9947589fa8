###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926351119",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}

###  审核
PUT {{baseAdminKernelUrl}}/kernel/patient/third-party/inquiry/pre-inquiry-audit
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "pref": "YWZ100114",
  "auditStatus": 1
}


### 2.处方划价 HYWZ100382
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-prescription/pricing
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "prescriptionPref": "HYWZ100922",
  "setlInvoiceNumber": "fph002",
  "type": 2,
  "detailPricingVos": [
    {
      "id": 1906629509859659777,
      "productPrice": "15.9"
    }
  ]
}


### 3.商家-医生-药师 查看问诊处方信息(状态+用药申请单+处方笺) HYWZ100463
GET {{baseAdminKernelUrl}}/kernel/patient/inquiry-query/get-record?inquiryPref=112017
Content-Type: application/json
Authorization: Bearer {{token}}


### 4.商家-医生-药师 查看问诊处方信息(状态+用药申请单+处方笺) HYWZ100463
GET {{baseAdminKernelUrl}}/kernel/patient/inquiry-query/get-prescription-by-pref?inquiryPref=111419
Content-Type: application/json
Authorization: Bearer {{token}}



### 5.
GET {{baseAdminKernelUrl}}/kernel/hospital/inquiry-prescription/get?id=1904793807169654785
Content-Type: application/json
Authorization: Bearer {{token}}


### 6.批量导出pdf-zip
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-prescription/export-pdf
Content-Type: application/json
Authorization: Bearer {{token}}


{
  "ids": [
    1920310358391492609,
    1920312389940383746,
    1920348089654022146
  ]
}


### 7.批量拼接打印pdf
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-prescription/batch-combiner-print
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "ids": [
    1910605296984047618,
    1910594241507540994,
    1910572114330959874
  ]
}





### 7.刷处方
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-prescription/flush
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "prefs": [
    "HYWZ102237"
  ],
  "tenantId": null,
  "status": null,
  "startTime": "2025-06-01 18:58:09",
  "endTime": "2025-06-12 18:00:44"
}



### 7.app首页菜单获取
GET {{baseAdminKernelUrl}}/kernel/drugstore/base-info/inquiry-permission
Content-Type: application/json
Authorization: Bearer {{token}}
