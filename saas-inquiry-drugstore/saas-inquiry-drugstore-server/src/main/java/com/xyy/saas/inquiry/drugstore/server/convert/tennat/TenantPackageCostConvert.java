package com.xyy.saas.inquiry.drugstore.server.convert.tennat;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostMessageDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/05 14:36
 */
@Mapper
public interface TenantPackageCostConvert {

    TenantPackageCostConvert INSTANCE = Mappers.getMapper(TenantPackageCostConvert.class);


    TenantPackageCostDO tenantPackageCostDto2CostRelationDO(TenantPackageCostDto tenantPackageCostDto);


    default List<TenantPackageCostDO> tenantPackageCostDto2CostRelationDOLists(TenantPackageCostDto tenantPackageCostDto) {
        if (CollUtil.isEmpty(tenantPackageCostDto.getInquiryPackageItems())) {
            return null;
        }

        return tenantPackageCostDto.getInquiryPackageItems().stream().map(c -> {
            TenantPackageCostDO packageCostDO = tenantPackageCostDto2CostRelationDO(tenantPackageCostDto);
            packageCostDO.setInquiryWayType(c.getInquiryWayType());
            packageCostDO.setCost(c.isUnlimited() ? TenantPackageConstant.UN_LIMITED_COST : c.getCount());
            packageCostDO.setSurplusCost(c.isUnlimited() ? TenantPackageConstant.UN_LIMITED_COST : c.getCount());
            return packageCostDO;
        }).collect(Collectors.toList());
    }

    @Mapping(target = "startTime", ignore = true)
    @Mapping(target = "endTime", ignore = true)
    @Mapping(target = "status", ignore = true)
    TenantPackageCostReqVO tenantPackageCostUpdateStatusDto2Vo(TenantPackageCostDto packageCostDto);

    /**
     * 转换剩余额度套餐Item
     *
     * @param packageCostDOS
     * @return
     */
    default List<InquiryPackageItem> convertSurplusInquiryPackageItems(List<TenantPackageCostDO> packageCostDOS) {
        if (packageCostDOS == null || packageCostDOS.isEmpty()) {
            return new ArrayList<>();
        }
        return packageCostDOS.stream().map(c -> InquiryPackageItem.builder()
                .inquiryWayType(c.getInquiryWayType())
                .count(c.getSurplusCost())
                .unlimited(TenantPackageConstant.isUnlimitedCost(c.getSurplusCost())).build())
            .collect(Collectors.toList());
    }

    /**
     * 转换初始额度套餐Item
     *
     * @param packageCostDOS
     * @return
     */
    default List<InquiryPackageItem> convertCostInquiryPackageItems(List<TenantPackageCostDO> packageCostDOS) {
        if (packageCostDOS == null || packageCostDOS.isEmpty()) {
            return new ArrayList<>();
        }
        return packageCostDOS.stream().map(c -> InquiryPackageItem.builder()
                .inquiryWayType(c.getInquiryWayType())
                .count(c.getCost())
                .unlimited(TenantPackageConstant.isUnlimitedCost(c.getCost())).build())
            .collect(Collectors.toList());
    }

    TenantPackageCostDto convertCostDto(TenantPackageCostMessageDto msg);

    TenantPackageCostDto convertDto(TenantPackageCostDO packageCostDO);

    List<TenantPackageCostDto> convertDtos(List<TenantPackageCostDO> packageCostDOS);
}
