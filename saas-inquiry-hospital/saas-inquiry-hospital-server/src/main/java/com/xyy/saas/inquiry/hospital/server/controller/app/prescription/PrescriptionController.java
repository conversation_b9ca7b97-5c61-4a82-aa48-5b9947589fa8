package com.xyy.saas.inquiry.hospital.server.controller.app.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.IssuesPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionCancelVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionIssuesVO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: cxy
 */
@Tag(name = "APP+PC - 处方开具核心接口")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/prescription", "/app-api/kernel/hospital/prescription"})
@Validated
public class PrescriptionController {

    @Resource
    private PrescriptionService prescriptionService;

    @PutMapping("/grabbing-prescription")
    @Operation(summary = "医生抢处方")
    @PreAuthorize("@ss.hasRole('doctor')")
    @TraceNode(node = TraceNodeEnum.DOCTOR_RECEPTION_INQUIRY, prefLocation = "reqVo.inquiryPref")
    public CommonResult<?> grabbingPrescriptionByDoctor(@Valid @RequestBody PrescriptionGrabbingVO reqVo) {
        return prescriptionService.grabbingPrescriptionByDoctor(reqVo);
    }

    @PostMapping("/issues-prescription")
    @Operation(summary = "医生开具处方")
    @Idempotent(message = "处方开具中，请勿重复提交")
    @PreAuthorize("@ss.hasRole('doctor')")
    @TraceNode(node = TraceNodeEnum.DOCTOR_ISSUE_PRESCRIPTION , prefLocation = "reqVo.inquiryPref")
    public CommonResult<IssuesPrescriptionRespVO> issuesPrescription(@Valid @RequestBody PrescriptionIssuesVO reqVo) {
        reqVo.setDoctorUserId(WebFrameworkUtils.getLoginUserId());
        return prescriptionService.issuesPrescription(reqVo);
    }

    @PostMapping("/cancel-prescription")
    @Operation(summary = "医生取消处方")
    @Idempotent(message = "处方处理中，请勿重复提交")
    @PreAuthorize("@ss.hasRole('doctor')")
    @TraceNode(node = TraceNodeEnum.DOCTOR_CANCEL_PRESCRIPTION , prefLocation = "reqVo.inquiryPref")
    public CommonResult<?> cancelPrescription(@Valid @RequestBody PrescriptionCancelVO reqVo) {
        return prescriptionService.cancelPrescription(reqVo);
    }

    @PostMapping("/auto-cancel-prescription")
    @Operation(summary = "医生自动取消处方")
    @Idempotent(message = "处方处理中，请勿重复提交")
    public CommonResult<?> autoCancelPrescription(@Valid @RequestBody PrescriptionCancelVO prescriptionCancelVO) {
        return prescriptionService.autoCancelPrescription(prescriptionCancelVO);
    }

    @GetMapping("/timeout-config")
    @Operation(summary = "处方开具超时时间配置-倒计时分钟")
    @PreAuthorize("@ss.hasRole('doctor')")
    public CommonResult<Integer> issuePrescriptionTimeOutConfig() {
        return CommonResult.success(prescriptionService.issuePrescriptionTimeOutConfig());
    }


}
