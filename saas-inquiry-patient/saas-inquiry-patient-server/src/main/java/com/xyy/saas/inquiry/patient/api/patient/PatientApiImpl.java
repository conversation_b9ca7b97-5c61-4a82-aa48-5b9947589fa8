package com.xyy.saas.inquiry.patient.api.patient;

import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2025/06/09 17:17
 */
@DubboService
public class PatientApiImpl implements PatientApi {

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;
    ;

    @Override
    public void migratePatientInfos(List<MigrationPatientDto> list) {
        inquiryPatientInfoService.migratePatientInfos(list);
    }
}
