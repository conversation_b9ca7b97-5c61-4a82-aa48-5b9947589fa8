package com.xyy.saas.inquiry.patient.dal.dataobject.third;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 三方药品匹配失败记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_third_party_drug_match_fail_record")
@KeySequence("saas_third_party_drug_match_fail_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ThirdPartyDrugMatchFailRecordDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 三方平台代码
     */
    private Integer transmissionOrganId;
    /**
     * 药品名称
     */
    private String commonName;
    /**
     * 商品规格
     */
    private String attributeSpecification;
    /**
     * 69码
     */
    private String barCode;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 匹配失败原因
     */
    private String matchFailMsg;

}