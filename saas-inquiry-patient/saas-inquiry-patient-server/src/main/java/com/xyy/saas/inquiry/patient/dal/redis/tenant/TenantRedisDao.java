package com.xyy.saas.inquiry.patient.dal.redis.tenant;

import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.dal.redis.BaseRedisDao;
import com.xyy.saas.inquiry.util.RedisUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * @Author: xucao
 * @Date: 2024/12/18 20:08
 * @Description: 患者、门店端缓存
 */
@Repository
@Slf4j
public class TenantRedisDao extends BaseRedisDao {

    /**
     * 门店发起问诊后，将当前问诊单推入门店问诊已发起的问诊队列中
     *
     * @param tenantId    租户id
     * @param env      环境
     * @param inquiryPref 问诊单号
     */
    public void onDrugstoreInquiry(Long tenantId,String env, String inquiryPref) {
        RedisUtils.lPush(RedisKeyConstants.getDrugstoreInquiryKey(tenantId,env), inquiryPref);
    }

    /**
     * 获取门店(待接诊)的问诊列表
     *
     * @param tenantId 租户id
     * @return 门店(待接诊)的问诊单号列表
     */
    public List<String> getDrugstoreInquiryList(Long tenantId , String env) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDrugstoreInquiryKey(tenantId,env)).stream().map(Object::toString).distinct().toList());
    }

    /**
     * 获取门店接诊中的问诊单号列表
     *
     * @param tenantId 租户id
     * @param env      环境
     * @return 门店接诊中的问诊单号列表
     */
    public List<String> getDrugstoreCurrentInquiryList(Long tenantId, String env) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getDrugstoreCurrentInquiryKey(tenantId, env)).stream().map(Object::toString).distinct().toList());
    }

    /**
     * 移除门店问诊队列
     * @param key
     * @param inquiryPref
     */
    public void removeInquiryForDrugstore(String key , String inquiryPref){
        RedisUtils.lRem(key, inquiryPref);
    }


    /**
     * 门店端取消问诊
     *
     * @param inquiryDto    问诊单
     * @param tenantDto     租户信息
     */
    public void onDrugstoreCancel(InquiryRecordDto inquiryDto, TenantDto tenantDto) {
       List<String> sendList = RedisUtils.lGetAll(RedisKeyConstants.getSendInquiryKey(inquiryDto.getPref())).stream().map(Object::toString).toList();
        // 获取当前问诊调度的科室 <科室编码>
        List<String> dispatchDoctorList = RedisUtils.lGetAll(RedisKeyConstants.getInquiryDispatchDeptKey(inquiryDto.getPref())).stream().map(Object::toString).toList();
        executeRedisTransaction(operations -> {
            // 1、移除-门店(待接诊)缓存
            operations.opsForList().remove(RedisKeyConstants.getDrugstoreInquiryKey(tenantDto.getId(),tenantDto.getEnvTag()), 0, inquiryDto.getPref());
            // 2、移除-从接诊大厅移除当前问诊
            dispatchDoctorList.forEach(deptPref -> {
                operations.opsForList().remove(RedisKeyConstants.getRecetionAreaKey(inquiryDto.getHospitalPref(), deptPref, inquiryDto.getAutoInquiry(), inquiryDto.getInquiryWayType()), 0, inquiryDto.getPref());
            });
            // 同步删除科室调度记录
            operations.delete(RedisKeyConstants.getInquiryDispatchDeptKey(inquiryDto.getPref()));
            // 3、移除-真人问诊需要移除问诊派单医生记录以及医生可接诊未抢单队列
            if (inquiryDto.isManualInquiry()) {
                sendList.forEach(doctorPref -> {
                    operations.opsForList().remove(RedisKeyConstants.getDoctorCanReceptionKey(doctorPref.toString(), tenantDto.getEnvTag()), 0, inquiryDto.getPref());
                    operations.opsForList().remove(RedisKeyConstants.getSendInquiryKey(inquiryDto.getPref()), 0, doctorPref);
                });
            }
        });
    }
}
