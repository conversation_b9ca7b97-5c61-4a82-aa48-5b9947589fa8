package com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.signature.InquiryPharmacistSignatureConvert;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 医师签章
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:30
 */
@Component("doctorSign")
@Slf4j
public class DoctorSignaturePassingStrategy extends AbstractInquirySignaturePassingStrategy {

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @Autowired
    protected InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    /**
     * 1.修改问诊状态 2.处方回传saas
     *
     * @param sphDto
     */
    @Override
    public void handleSelf(SignaturePassingHandleDto sphDto) {
        if (ObjectUtil.isNull(sphDto.getSpMessage()) || ObjectUtil.isNull(sphDto.getPrescription())) {
            return;
        }
        // 医生回调补审核记录
        InquiryPrescriptionAuditSaveReqVO auditSaveReqVO = InquiryPharmacistSignatureConvert.INSTANCE.convertSaveAuditRecord(sphDto, PrescriptionAuditStatusEnum.APPROVED, SignatureStatusEnum.SIGNED);
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(sphDto.getPrescription().getInquiryPref());
        if (inquiryRecord != null) {
            InquiryDoctorDto doctor = inquiryDoctorApi.getInquiryDoctorByDoctorPref(inquiryRecord.getDoctorPref());
            InquiryPharmacistSignatureConvert.INSTANCE.fillRecord2AuditVO(auditSaveReqVO, sphDto.getPrescription(), inquiryRecord, doctor);
        }
        inquiryPrescriptionAuditService.createPrescriptionAudit(auditSaveReqVO);

        // 开具处方后置处理 如划价等
        inquiryPrescriptionApi.postProcessIssuesPrescription(sphDto.getPrescription().getPref());

        // IM通知商家处方开具
        inquiryPharmacistImService.sendPrescriptionDrawnFinish(sphDto.getPrescription().getInquiryPref());

    }


    @Override
    public void executeNext(SignaturePassingHandleDto sphDto) {
        // 医师签章目前是第一级节点，没有业务的下一级是此service
    }
}
