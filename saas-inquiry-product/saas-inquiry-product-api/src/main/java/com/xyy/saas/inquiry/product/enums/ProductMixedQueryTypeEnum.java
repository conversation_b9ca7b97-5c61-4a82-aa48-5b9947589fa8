package com.xyy.saas.inquiry.product.enums;


import java.util.Objects;

/**
 * desc 商品关键字搜索类型：1 - 多个条形码；2 - 多个商品编码；3 - 多个标准库ID；4 - 单个通用名称；5 - 单个品牌名称
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum ProductMixedQueryTypeEnum {

    // 多个条形码
    MULTI_BARCODE(1, "多个条形码"),
    // 多个商品编码
    MULTI_SHOW_PREF(2, "多个商品编码"),
    // 多个标准库ID
    MULTI_MID_STDLIB_ID(3, "多个标准库ID"),
    // 单个通用名称
    COMMON_NAME(4, "单个通用名称"),
    // 单个品牌名称
    BRAND_NAME(5, "单个品牌名称")
    ;

    public final int code;
    public final String desc;

    ProductMixedQueryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code
     * @return
     */
    public static ProductMixedQueryTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductMixedQueryTypeEnum type : values()) {
            if (Objects.equals(type.code, code)) {
                return type;
            }
        }
        return null;
    }



}
