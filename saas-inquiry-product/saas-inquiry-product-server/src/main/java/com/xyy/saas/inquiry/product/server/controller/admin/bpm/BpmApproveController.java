package com.xyy.saas.inquiry.product.server.controller.admin.bpm;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovePageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApproveRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.collections4.CollectionUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Tag(name = "管理后台 - 审批流管理")
@RestController
@RequestMapping("/bpm/approve")
@Validated
public class BpmApproveController {

    @Resource
    private BpmBusinessRelationService bpmBusinessRelationService;
    @Resource
    private ProductInfoService productInfoService;
    
    @GetMapping("/product-first-approve/page")
    @Operation(summary = "首营商品审批分页查询")
    @PreAuthorize("@ss.hasPermission('saas:bpm:approve:query')")
    public CommonResult<PageResult<BpmApproveRespVO>> page(@Valid @ParameterObject BpmApprovePageReqVO pageReqVO) {
        // 使用连表查询直接获取商品信息
        PageResult<BpmApproveRespVO> pageResult = bpmBusinessRelationService.getBpmBusinessRelationPageWithProductInfo(pageReqVO);
        
        return success(pageResult);
    }

    @GetMapping("/get-by-process-instance-id")
    @Operation(summary = "获得审批关联业务详情（审批流）")
    @Parameter(name = "processInstanceId", description = "流程实例id", required = true, example = "SP001")
    @PreAuthorize("@ss.hasPermission('saas:bpm:approve:query')")
    public CommonResult<BpmApproveRespVO> getProductInfoByProcessInstanceId(@RequestParam("processInstanceId") String processInstanceId) {
        // 流程实例信息
        List<BpmProcessInstanceRespVO> bpmProcessInstanceRespVOList = bpmBusinessRelationService.buildProcessInstanceList(Set.of(processInstanceId));
        if (CollectionUtils.isEmpty(bpmProcessInstanceRespVOList)) {
            return success(null);
        }

        BpmProcessInstanceRespVO bpmProcessInstanceRespVO = bpmProcessInstanceRespVOList.getFirst();
        BpmApproveRespVO respVO = BeanUtils.toBean(bpmProcessInstanceRespVO, BpmApproveRespVO.class);

        // 业务关联信息
        BpmBusinessRelationDO bpmBusinessRelation = bpmBusinessRelationService.getBpmBusinessRelation(processInstanceId);
        BpmBusinessRelationRespVO bizRlt = BeanUtils.toBean(bpmBusinessRelation, BpmBusinessRelationRespVO.class);
        respVO.setBusinessRelation(bizRlt);

        // 首营商品审批
        if (bizRlt != null && Objects.equals(bizRlt.getBusinessType(), BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE.code)) {
            // 商品信息
            List<ProductInfoDto> productInfoList = productInfoService.listProductInfoByPref(List.of(bizRlt.getBusinessPref()), true);
            if (CollectionUtils.isNotEmpty(productInfoList)) {
                respVO.setProductInfo(BeanUtils.toBean(productInfoList.getFirst(), ProductInfoRespVO.class));
            }
        }

        return success(respVO);
    }
} 