package com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo;

import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 审批 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmApproveRespVO extends BpmBusinessRelationRespVO2 {
    
    @Schema(description = "商品信息")
    private ProductInfoRespVO productInfo;

    //
} 