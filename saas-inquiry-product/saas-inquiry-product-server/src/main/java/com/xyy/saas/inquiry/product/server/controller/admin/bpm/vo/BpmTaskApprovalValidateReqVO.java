package com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

@Schema(description = "管理后台 - 流程任务审批验证 Request VO")
@Data
public class BpmTaskApprovalValidateReqVO {
    
    @Schema(description = "任务编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "任务编号不能为空")
    private String taskId;
    
    @Schema(description = "验证器类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "password")
    @NotEmpty(message = "验证器类型不能为空")
    private String validatorType;
    
    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
    
    @Schema(description = "验证参数")
    private Map<String, Object> params;
} 