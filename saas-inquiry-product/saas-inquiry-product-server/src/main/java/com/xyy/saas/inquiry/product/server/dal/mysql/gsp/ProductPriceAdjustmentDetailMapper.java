package com.xyy.saas.inquiry.product.server.dal.mysql.gsp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentDetailRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeDetailRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 售价调整单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductPriceAdjustmentDetailMapper extends BaseMapperX<ProductPriceAdjustmentDetailDO> {

    default PageResult<ProductPriceAdjustmentDetailDO> selectPage(ProductPriceAdjustmentDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductPriceAdjustmentDetailDO>()
            .eqIfPresent(ProductPriceAdjustmentDetailDO::getRecordPref, reqVO.getRecordPref())
            .eqIfPresent(ProductPriceAdjustmentDetailDO::getApplicableTenantId, reqVO.getApplicableTenantId())
            .eqIfPresent(ProductPriceAdjustmentDetailDO::getProductPref, reqVO.getProductPref())
            .betweenIfPresent(ProductPriceAdjustmentDetailDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(ProductPriceAdjustmentDetailDO::getId));
    }

    default PageResult<ProductPriceAdjustmentDetailDO> selectPage(PageParam reqVO, String recordPref) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductPriceAdjustmentDetailDO>()
            .eq(ProductPriceAdjustmentDetailDO::getRecordPref, recordPref)
            .orderByDesc(ProductPriceAdjustmentDetailDO::getId));
    }

    default int deleteByRecordPref(String recordPref) {
        return delete(ProductPriceAdjustmentDetailDO::getRecordPref, recordPref);
    }

    Page<ProductPriceAdjustmentDetailRespVO> selectPageWithProductInfo(Page<Object> page, @Param("req") ProductPriceAdjustmentDetailPageReqVO req);
}