package com.xyy.saas.inquiry.product.server.dal.mysql.product;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibExtDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductUpdateDto;
import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductUpdateReqVo;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_STDLIB_STATUS_IN_USING;

@Mapper
public interface ProductStdlibMapper extends BaseMapperX<ProductStdlibDO> {

    /**
     * 根据六要素信息查询唯一标准库数据（兼容数据库 NOT NULL DEFAULT '' ）
     *
     * @param sixKeyPointDto
     * @return
     */
    default ProductStdlibDO uniqueQuery(ProductInfoDto sixKeyPointDto) {
        // 克隆对象（不影响原对象）
        ProductStdlibDO stdlib = BeanUtils.toBean(sixKeyPointDto, ProductStdlibDO.class);
        // 计算要素字段hash值
        stdlib.calcMnemonicCodeAndKeyPointHash();

        List<ProductStdlibDO> list = selectList(new LambdaQueryWrapperX<ProductStdlibDO>()
            .eq(ProductStdlibDO::getKeyPointHash, stdlib.getKeyPointHash())
            .eq(ProductStdlibDO::getCommonName, StringUtils.defaultIfBlank(stdlib.getCommonName(), ""))
            .eq(ProductStdlibDO::getBrandName, StringUtils.defaultIfBlank(stdlib.getBrandName(), ""))
            .eq(ProductStdlibDO::getBarcode, StringUtils.defaultIfBlank(stdlib.getBarcode(), ""))
            .eq(ProductStdlibDO::getSpec, StringUtils.defaultIfBlank(stdlib.getSpec(), ""))
            .eq(ProductStdlibDO::getApprovalNumber, StringUtils.defaultIfBlank(stdlib.getApprovalNumber(), ""))
            .eq(ProductStdlibDO::getManufacturer, StringUtils.defaultIfBlank(stdlib.getManufacturer(), ""))
        );
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 优先返回使用中的，如果没有则取第一条返回
        return list.stream().filter(i -> Objects.equals(i.getStatus(), ProductStdlibStatusEnum.USING.code)).findFirst()
            .orElseGet(list::getFirst);
    }

    /**
     * 分页查询标准库数据（全文索引）
     *
     * @param stdlib
     * @return
     */
    default PageResult<ProductStdlibDO> pageQuery(StdlibProductPageQueryVo stdlib, boolean isMidStdlib) {
        LambdaQueryWrapperX<ProductStdlibDO> wrapper = new LambdaQueryWrapperX<ProductStdlibDO>()
            .eqIfPresent(ProductStdlibDO::getSpuCategory, stdlib.getSpuCategory())
            .eqIfPresent(ProductStdlibDO::getBarcode, stdlib.getBarcode())
            .likeIfPresent(ProductStdlibDO::getCommonName, stdlib.getCommonName())
            .likeIfPresent(ProductStdlibDO::getSpec, stdlib.getSpec())
            .likeIfPresent(ProductStdlibDO::getApprovalNumber, stdlib.getApprovalNumber())
            .likeIfPresent(ProductStdlibDO::getManufacturer, stdlib.getManufacturer())
            .eqIfPresent(ProductStdlibDO::getMidStdlibId, stdlib.getMidStdlibId())
            .eqIfPresent(ProductStdlibDO::getPresCategory, stdlib.getPresCategory())
            .eqIfPresent(ProductStdlibDO::getFirstCategory, stdlib.getFirstCategory())
            .eqIfPresent(ProductStdlibDO::getSecondCategory, stdlib.getSecondCategory())
            .eqIfPresent(ProductStdlibDO::getThirdCategory, stdlib.getThirdCategory())
            .eqIfPresent(ProductStdlibDO::getFourthCategory, stdlib.getFourthCategory())
            .eqIfPresent(ProductStdlibDO::getFiveCategory, stdlib.getFiveCategory())
            .eqIfPresent(ProductStdlibDO::getSixCategory, stdlib.getSixCategory())
            .eqIfPresent(ProductStdlibDO::getStatus, stdlib.getStatus())
            .eqIfPresent(ProductStdlibDO::getDisable, stdlib.getDisable());

        if (stdlib.getMultiFlag() != null) {
            wrapper.apply(ProductFlag.toStdlibFlagSql(stdlib.getMultiFlag(), "multi_flag"));
        }
        // 如果全是数字，不走全文索引，走条形码模糊匹配
        if (StringUtils.isNotBlank(stdlib.getMixedQuery())) {
            if (stdlib.getMixedQuery().matches("\\d+")) {
                allNumberQuery(stdlib::getMixedQuery, wrapper);
            } else {
                wrapper.apply("MATCH (common_name, brand_name, barcode, mnemonic_code, approval_number, manufacturer) AGAINST ({0,javaType=String,jdbcType=VARCHAR} IN BOOLEAN MODE)", stdlib.getMixedQuery());
            }
        }
        if (StringUtils.isNotBlank(stdlib.getNatureQuery())) {
            if (stdlib.getNatureQuery().matches("\\d+")) {
                allNumberQuery(stdlib::getNatureQuery, wrapper);
            } else {
                wrapper.apply("MATCH (common_name, brand_name, barcode, mnemonic_code, approval_number, manufacturer) AGAINST ({0,javaType=String,jdbcType=VARCHAR})", stdlib.getNatureQuery());
            }
        }
        if (StringUtils.isNotBlank(stdlib.getMixedNameQuery())) {
            if (stdlib.getMixedNameQuery().matches("\\d+")) {
                allNumberQuery(stdlib::getMixedNameQuery, wrapper);
            } else {
                wrapper.apply("MATCH (common_name, brand_name, mnemonic_code) AGAINST ({0,javaType=String,jdbcType=VARCHAR} IN BOOLEAN MODE)", stdlib.getMixedNameQuery());
            }
        }
        if (StringUtils.isNotBlank(stdlib.getNatureNameQuery())) {
            if (stdlib.getNatureNameQuery().matches("\\d+")) {
                allNumberQuery(stdlib::getNatureNameQuery, wrapper);
            } else {
                wrapper.apply("MATCH (common_name, brand_name, mnemonic_code) AGAINST ({0,javaType=String,jdbcType=VARCHAR})", stdlib.getNatureNameQuery());
            }
        }
        // 判断是否关联有中台库数据
        if (isMidStdlib) {
            wrapper.isNotNull(ProductStdlibDO::getMidStdlibId);
        } else {
            wrapper.isNull(ProductStdlibDO::getMidStdlibId);
        }

        return selectPage(stdlib, wrapper);
    }

    /**
     * 全数字查询：条形码模糊匹配 (作废：or 中台标准库id精准匹配)
     * @param queryNumber
     * @param wrapper
     */
    private static void allNumberQuery(Supplier<String> queryNumber, LambdaQueryWrapperX<ProductStdlibDO> wrapper) {
        String number = queryNumber.get();
        wrapper.like(ProductStdlibDO::getBarcode, number);
        // wrapper.and(w -> w
        //     .like(ProductStdlibDO::getBarcode, number)
        //     .or().eq(ProductStdlibDO::getMidStdlibId, number)
        // );
    }

    /**
     * 根据中台标准库id查询标准库数据
     *
     * @param midStdlibIds
     * @return
     */
    default List<ProductStdlibDO> listByMidStdlibId(List<Long> midStdlibIds) {
        if (CollectionUtils.isEmpty(midStdlibIds)) {
            return List.of();
        }

        return selectList(new LambdaQueryWrapperX<ProductStdlibDO>()
            .in(ProductStdlibDO::getMidStdlibId, midStdlibIds)
        );
    }

    /**
     * 更新标准库数据
     *
     * @param dto            商品信息DTO
     * @param existStdlib    已存在的标准库数据
     * @param ignoreSixPoint 是否忽略六要素字段更新
     * @return 更新记录数
     */
    default ProductStdlibDO updateWithCheckUnique(ProductInfoDto dto, ProductStdlibDO existStdlib, boolean ignoreSixPoint) {
        if (dto == null || existStdlib == null) {
            return null;
        }
        // 构建更新对象,仅更新非六要素字段
        ProductStdlibDO updateDO = ProductConvert.INSTANCE.ProductInfoDto2ProductStdlibDO(dto);
        updateDO.setId(existStdlib.getId());
        updateDO.setMultiFlag(ProductFlag.toStdlibFlag(existStdlib.getMultiFlag(), dto.getProductFlag()));
        if (updateDO.getExt() != null) {
            updateDO.getExt().migrate(existStdlib.getExt());
        }

        // 设置状态
        ProductStdlibStatusEnum productStdlibStatusEnum = ProductStdlibStatusEnum.fromProductStatus(dto.getStatus());
        updateDO.setStatus(Optional.ofNullable(productStdlibStatusEnum).map(i -> i.code).orElse(null));

        // 判断是否其他字段有修改, 如果没有则不需要更新
        if (existStdlib.isEquals(updateDO, ignoreSixPoint, true)) {
            return existStdlib;
        }

        return insertOrUpdateWithCheckUnique(updateDO, ignoreSixPoint);
    }

    /**
     * 插入或更新标准库数据（唯一约束检测）
     *
     * @param stdlib
     * @return
     */
    default ProductStdlibDO insertOrUpdateWithCheckUnique(ProductStdlibDO stdlib) {
        return insertOrUpdateWithCheckUnique(stdlib, false);
    }
    default ProductStdlibDO insertOrUpdateWithCheckUnique(ProductStdlibDO stdlib, boolean ignoreSixPoint) {
        if (stdlib == null) {
            return null;
        }
        // 判断中台标准库id是否重复
        return insertOrUpdateWithCheckUnique(List.of(stdlib), ignoreSixPoint).getFirst();
    }

    default List<ProductStdlibDO> insertOrUpdateWithCheckUnique(List<ProductStdlibDO> stdlibList) {
        return insertOrUpdateWithCheckUnique(stdlibList, false);
    }

    // 所有更新五要素字段，都需要走这个方法！！！
    default List<ProductStdlibDO> insertOrUpdateWithCheckUnique(List<ProductStdlibDO> stdlibList, boolean ignoreSixPoint) {
        if (CollectionUtils.isEmpty(stdlibList)) {
            return stdlibList;
        }

        // 计算助记码、要素字段hash值
        stdlibList.forEach(i -> i.calcMnemonicCodeAndKeyPointHash(ignoreSixPoint));

        List<Long> midStdlibIdList = stdlibList.stream().map(ProductStdlibDO::getMidStdlibId).filter(Objects::nonNull).distinct().toList();
        Map<Long, ProductStdlibDO> midStdlibIdMap = listByMidStdlibId(midStdlibIdList).stream().collect(Collectors.toMap(ProductStdlibDO::getMidStdlibId, Function.identity(), (a, b) -> b));

        List<ProductStdlibDO> procList = new ArrayList<>();
        stdlibList.forEach(stdlib -> {
            ProductStdlibDO duplicateMidStdlib = midStdlibIdMap.get(stdlib.getMidStdlibId());
            // 判断中台标准库id是否重复
            if (isDuplicateMidStdlibId(stdlib.getMidStdlibId(), stdlib.getId(), duplicateMidStdlib)) {
                // 标准库商品状态使用中不能改成其他状态
                if (Objects.equals(ProductStdlibStatusEnum.USING.code, duplicateMidStdlib.getStatus())
                    && stdlib.getStatus() != null && !Objects.equals(ProductStdlibStatusEnum.USING.code, stdlib.getStatus())) {
                    throw exception(PRODUCT_STDLIB_STATUS_IN_USING);
                }
                // 重复的中台标准库id，根据id修改其他字段
                ProductStdlibDO clone = stdlib.clone()
                    .setId(duplicateMidStdlib.getId());
                // 顺序不能改，clone的在前
                procList.add(clone);

                // 原自建数据不更新状态
                stdlib.setMidStdlibIdBak(stdlib.getMidStdlibId())
                    .setMidStdlibId(null)
                    .setStatus(null);
                procList.add(stdlib);
                return;
            }
            if (stdlib.getMidStdlibId() != null) {
                stdlib.setMidStdlibIdBak(stdlib.getMidStdlibId());
            }
            procList.add(stdlib);
        });

        // 批量插入 or 更新
        procList.stream().collect(Collectors.groupingBy(i -> i.getId() == null))
            .forEach((isInsert, l) -> {
                if (isInsert) {
                    insertBatch(l);
                } else {
                    // 更新, 五要素可能只更新部分字段，hash计算不准确，需要再次刷新数据库数据
                    updateById(l);
                    batchRefreshKeyPointHash(l.stream().map(ProductStdlibDO::getId).toList());
                }
            });
        return procList;
    }

    /**
     * 判断中台标准库id是否重复
     *
     * @param midStdlibId
     * @param id
     * @return
     */
    default boolean isDuplicateMidStdlibId(Long midStdlibId, Long id, ProductStdlibDO stdlibDO) {
        if (midStdlibId == null) {
            return false;
        }
        if (stdlibDO == null) {
            stdlibDO = listByMidStdlibId(List.of(midStdlibId)).stream().findFirst().orElse(null);
        }
        if (stdlibDO == null) {
            return false;
        }
        return !stdlibDO.getId().equals(id);
    }

    /**
     * 批量更新标准库数据（停用状态、中台同步是否覆盖）
     * @param ids
     * @return
     */
    int batchRefreshKeyPointHash(@Param("ids") List<Long> ids);

    /**
     * 批量更新标准库数据（停用状态、中台同步是否覆盖）
     * @param param
     * @return
     */
    int batchUpdateStdlib(@Param("param") StdlibProductUpdateDto param);

    /**
     * 查询自建标准库中去重后的商品通用名列表
     * @param param
     * @param limit
     * @return
     */
    List<ProductStdlibDO> selectDistinctCommonName(@Param("param") StdlibProductSearchDto param, @Param("limit") int limit, @Param("offset") Integer offset);

    /**
     * 查询自建标准库商品
     * @param param
     * @param limit
     * @param offset
     * @return
     */
    List<ProductStdlibDO> searchStdlibProductList(@Param("param") StdlibProductSearchDto param, @Param("limit") int limit, @Param("offset") Integer offset);

    /**
     * 查询自建标准库商品（根据通用名+规则分组去重，组内随机取一条）
     * @param param
     * @param limit
     * @return
     */
    List<ProductStdlibDO> searchStdlibProductListGroupByNameSpec(@Param("param") StdlibProductSearchDto param, @Param("limit") int limit);
}