package com.xyy.saas.inquiry.product.server.service.bpm;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.form.BpmFormPageReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.form.BpmFormSaveReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.model.BpmModelPageReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.model.BpmModelSaveReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.model.simple.BpmSimpleModelNodeVO;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.model.simple.BpmSimpleModelUpdateReqVO;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmFormDO;
import cn.iocoder.yudao.module.bpm.enums.definition.BpmModelFormTypeEnum;
import cn.iocoder.yudao.module.bpm.enums.definition.BpmModelTypeEnum;
import cn.iocoder.yudao.module.bpm.service.definition.BpmFormService;
import cn.iocoder.yudao.module.bpm.service.definition.BpmModelService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.repository.Model;
import org.springframework.stereotype.Service;

/**
 * 审批流程模型初始化 Service 实现类
 */
@Service
@Slf4j
public class BpmBusinessInitService {

    @Resource
    private BpmModelService modelService;

    @Resource
    private BpmFormService formService;
    
    @Resource
    private AdminUserApi adminUserApi;


    private String defaultCategory = "default";
    private String defaultFormName = "default";
    private String defaultIcon = "http://files.test.ybm100.com/INVT/Lzinq/20250206/181ccfbef2ac7bb1b98a4d6f5b64d5d1b9f11bd365e8f6d8ba04d3a3c67be575.png";

    /**
     * 初始化审批流程模型
     */
    public void initBpmModels() {
        log.info("[initBpmModels][开始初始化审批流程模型]");
        
        // 1. 查询现有审批流模型
        BpmModelPageReqVO pageReqVO = new BpmModelPageReqVO();
        pageReqVO.setCategory("default");
        pageReqVO.setPageSize(500); // 设置较大的页面大小，确保获取所有模型


        PageResult<Model> modelPage = modelService.getModelPage(pageReqVO);
        
        // 获取所有现有模型的key
        Set<String> existingModelKeys = modelPage.getList().stream()
                .map(Model::getKey)
                .collect(Collectors.toSet());
        
        log.info("[initBpmModels][现有审批流模型数量: {}]", existingModelKeys.size());
        
        // 2. 获取默认表单
        Long defaultFormId = getOrCreateDefaultForm();
        
        // 3. 获取管理员用户ID
        Long adminUserId = getAdminUserId();
        
        // 4. 遍历业务类型枚举，创建缺失的模型
        for (BpmBusinessTypeEnum businessType : BpmBusinessTypeEnum.values()) {
            String processDefinitionKey = businessType.processDefinitionKey;
            
            // 如果模型已存在，则跳过
            if (existingModelKeys.contains(processDefinitionKey)) {
                log.info("[initBpmModels][业务类型({})的流程模型已存在，跳过]", businessType.desc);
                continue;
            }
            
            // 创建模型
            String modelId = createModel(businessType, defaultFormId, adminUserId);
            
            // 设计流程
            designSimpleProcess(modelId, adminUserId);
            
            log.info("[initBpmModels][业务类型({})的流程模型创建成功]", businessType.desc);
        }
        
        log.info("[initBpmModels][审批流程模型初始化完成]");
    }

    /**
     * 获取管理员用户ID
     *
     * @return 管理员用户ID
     */
    private Long getAdminUserId() {
        // 获取当前租户的管理员用户
        AdminUserRespDTO adminUser = adminUserApi.getUser(1L); // 假设ID为1的用户是管理员
        if (adminUser == null) {
            log.warn("[getAdminUserId][未找到管理员用户，将使用默认ID]");
            return 1L;
        }
        return adminUser.getId();
    }
    
    /**
     * 获取或创建默认表单
     * 
     * @return 默认表单ID
     */
    private Long getOrCreateDefaultForm() {
        // 查询默认表单
        BpmFormPageReqVO formPageReqVO = new BpmFormPageReqVO();
        formPageReqVO.setName(defaultFormName);
        PageResult<BpmFormDO> formPage = formService.getFormPage(formPageReqVO);
        
        // 如果默认表单存在，直接返回ID
        if (CollUtil.isNotEmpty(formPage.getList())) {
            return formPage.getList().getFirst().getId();
        }
        
        // 创建默认表单
        BpmFormSaveReqVO createReqVO = new BpmFormSaveReqVO();
        createReqVO.setName(defaultFormName);
        createReqVO.setStatus(0);
        createReqVO.setRemark("系统默认表单");
        createReqVO.setConf("{\"form\":{\"inline\":false,\"hideRequiredAsterisk\":false,\"labelPosition\":\"right\",\"size\":\"default\",\"labelWidth\":\"125px\",\"showMessage\":false},\"resetBtn\":{\"show\":false,\"innerText\":\"重置\"},\"submitBtn\":{\"show\":false,\"innerText\":\"提交\"}}");
        createReqVO.setFields(new ArrayList<>());
        
        Long formId = formService.createForm(createReqVO);
        log.info("[getOrCreateDefaultForm][创建默认表单成功，表单ID: {}]", formId);
        
        return formId;
    }
    
    /**
     * 创建审批流模型
     * 
     * @param businessType 业务类型枚举
     * @param formId 表单ID
     * @param adminUserId 管理员用户ID
     * @return 创建的模型ID
     */
    private String createModel(BpmBusinessTypeEnum businessType, Long formId, Long adminUserId) {
        BpmModelSaveReqVO createReqVO = new BpmModelSaveReqVO();
        createReqVO.setKey(businessType.processDefinitionKey);
        createReqVO.setName(businessType.desc);
        createReqVO.setCategory(defaultCategory);
        createReqVO.setIcon(defaultIcon);
        createReqVO.setDescription("");
        createReqVO.setType(BpmModelTypeEnum.SIMPLE.getType());
        createReqVO.setFormType(BpmModelFormTypeEnum.NORMAL.getType());
        createReqVO.setFormId(formId);
        createReqVO.setVisible(true);
        createReqVO.setStartUserIds(Collections.emptyList());
        createReqVO.setManagerUserIds(List.of(adminUserId));
        
        return modelService.createModel(createReqVO);
    }
    
    /**
     * 设计简单流程
     * 
     * @param modelId 模型ID
     */
    private void designSimpleProcess(String modelId, Long userId) {
        BpmSimpleModelUpdateReqVO updateReqVO = new BpmSimpleModelUpdateReqVO();
        updateReqVO.setId(modelId);
        
        // 创建标准的三级审批流程
        BpmSimpleModelNodeVO processData = buildStandardThreeLevelApprovalProcess();
        updateReqVO.setSimpleModel(processData);
        
        modelService.updateSimpleModel(userId, updateReqVO);
    }
    
    /**
     * 构建标准的三级审批流程数据
     * 
     * @return 流程数据JSON字符串
     */
    private BpmSimpleModelNodeVO buildStandardThreeLevelApprovalProcess() {
        // 构建标准的三级审批流程模板
        // 包含：发起人节点、一级审批节点、二级审批节点、三级审批节点和结束节点
        return "{\n" +
                "  \"nodes\": [\n" +
                "    {\n" +
                "      \"id\": \"start\",\n" +
                "      \"type\": \"START\",\n" +
                "      \"name\": \"开始\",\n" +
                "      \"x\": 250,\n" +
                "      \"y\": 250\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"userTask1\",\n" +
                "      \"type\": \"USER_TASK\",\n" +
                "      \"name\": \"一级审批\",\n" +
                "      \"x\": 400,\n" +
                "      \"y\": 250,\n" +
                "      \"candidateStrategy\": 30,\n" +
                "      \"approveType\": 1,\n" +
                "      \"approveMethod\": 1,\n" +
                "      \"rejectHandler\": {\n" +
                "        \"type\": 1\n" +
                "      },\n" +
                "      \"assignEmptyHandler\": {\n" +
                "        \"type\": 1\n" +
                "      },\n" +
                "      \"assignStartUserHandler\": {\n" +
                "        \"type\": 1\n" +
                "      }\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"userTask2\",\n" +
                "      \"type\": \"USER_TASK\",\n" +
                "      \"name\": \"二级审批\",\n" +
                "      \"x\": 550,\n" +
                "      \"y\": 250,\n" +
                "      \"candidateStrategy\": 30,\n" +
                "      \"approveType\": 1,\n" +
                "      \"approveMethod\": 1,\n" +
                "      \"rejectHandler\": {\n" +
                "        \"type\": 1\n" +
                "      },\n" +
                "      \"assignEmptyHandler\": {\n" +
                "        \"type\": 1\n" +
                "      },\n" +
                "      \"assignStartUserHandler\": {\n" +
                "        \"type\": 1\n" +
                "      }\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"userTask3\",\n" +
                "      \"type\": \"USER_TASK\",\n" +
                "      \"name\": \"三级审批\",\n" +
                "      \"x\": 700,\n" +
                "      \"y\": 250,\n" +
                "      \"candidateStrategy\": 30,\n" +
                "      \"approveType\": 1,\n" +
                "      \"approveMethod\": 1,\n" +
                "      \"rejectHandler\": {\n" +
                "        \"type\": 1\n" +
                "      },\n" +
                "      \"assignEmptyHandler\": {\n" +
                "        \"type\": 1\n" +
                "      },\n" +
                "      \"assignStartUserHandler\": {\n" +
                "        \"type\": 1\n" +
                "      }\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"end\",\n" +
                "      \"type\": \"END\",\n" +
                "      \"name\": \"结束\",\n" +
                "      \"x\": 850,\n" +
                "      \"y\": 250\n" +
                "    }\n" +
                "  ],\n" +
                "  \"edges\": [\n" +
                "    {\n" +
                "      \"id\": \"flow1\",\n" +
                "      \"sourceId\": \"start\",\n" +
                "      \"targetId\": \"userTask1\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"flow2\",\n" +
                "      \"sourceId\": \"userTask1\",\n" +
                "      \"targetId\": \"userTask2\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"flow3\",\n" +
                "      \"sourceId\": \"userTask2\",\n" +
                "      \"targetId\": \"userTask3\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"id\": \"flow4\",\n" +
                "      \"sourceId\": \"userTask3\",\n" +
                "      \"targetId\": \"end\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }
}