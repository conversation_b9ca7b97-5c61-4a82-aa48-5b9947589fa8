package com.xyy.saas.inquiry.product.server.service.product;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_PRESENT_FAILED;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_STDLIB_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.multi.RowKeyTable;
import cn.hutool.core.map.multi.Table;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataSaveBatchDto;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.enums.ProductSpuCategoryEnum;
import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.enums.ProductTransferTypeEnum;
import com.xyy.saas.inquiry.product.server.annotation.DictFieldType;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralMatchProductNewDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductPresentDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidPictureProResult;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse2;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidSaasWithMixSearchInfoDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductMixedPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductUpdateReqVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductVo;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.convert.product.ProductConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibMapper;
import com.xyy.saas.inquiry.product.server.mq.producer.mid.MidStdlibInteractiveProducer;
import com.xyy.saas.inquiry.product.server.service.product.mid.ProductMidStdlibService;
import com.xyy.saas.inquiry.product.server.service.productcategory.ProductCategoryService;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
@Validated
@Slf4j
public class ProductStdlibServiceImpl implements ProductStdlibService {

    @Resource
    private ProductStdlibMapper productStdlibMapper;
    @Resource
    private ProductInfoMapper productInfoMapper;

    @Resource
    private ProductMidStdlibService midStdlibService;
    @Resource
    private ProductTransferRecordService transferRecordService;

    @Resource
    private ProductCategoryService categoryService;

    @Resource
    private TenantApi tenantApi;
    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private MidStdlibInteractiveProducer midStdlibInteractiveProducer;


    /**
     * 新增或更新商品标准库（以中台数据覆盖）
     *
     * @param midStdlibIdList
     * @param midStdlibIdList
     */
    @Override
    @Transactional
    public List<ProductStdlibDO> saveOrUpdateStdlibFromMid(List<Long> midStdlibIdList, boolean imageSync) {
        if (CollectionUtils.isEmpty(midStdlibIdList)) {
            return List.of();
        }
        // 去重
        midStdlibIdList = midStdlibIdList.stream().distinct().toList();
        // 查询中台标准库数据
        List<MidGeneralProductDto> midStdlibList = midStdlibService.getGeneralProductList(midStdlibIdList);
        if (CollectionUtils.isEmpty(midStdlibList)) {
            return List.of();
        }

        // 再次过滤标准库id
        midStdlibIdList = midStdlibList.stream().map(i -> NumberUtils.toLong(i.getProductId())).distinct().toList();
        // 查询中台标准库数据（图片）
        Map<String, MidPictureProResult> midStdlibPicMap = !imageSync || CollectionUtils.isEmpty(midStdlibIdList) ? Map.of()
            : midStdlibService.findProPicture(midStdlibIdList)
                .stream().collect(Collectors.toMap(MidPictureProResult::getProductId, Function.identity(), (a, b) -> b));

        // 查询自建标准库数据
        Map<Long, ProductStdlibDO> stdlibDOMap = CollectionUtils.isEmpty(midStdlibIdList) ? Map.of()
            : productStdlibMapper.listByMidStdlibId(midStdlibIdList)
                .stream().collect(Collectors.toMap(ProductStdlibDO::getMidStdlibId, Function.identity(), (a, b) -> b));

        // stdlibList 根据 midStdlibId 去重，打印出重复数据
        Map<Long, ProductStdlibDO> midStdlibIdMap = new HashMap<>();
        // 转换为标准库数据
        midStdlibList.forEach(i -> {
            ProductStdlibDO newStdlibDO = ProductConvert.INSTANCE.MidGeneralProductDto2ProductStdlibDO(i);
            Long midStdlibId = newStdlibDO.getMidStdlibId();
            ProductStdlibDO duplicatedDO = midStdlibIdMap.get(midStdlibId);
            if (duplicatedDO != null) {
                log.error("[saveOrUpdateStdlibFromMid][midStdlibId({}) 重复]", midStdlibId);
                // 比较 哪个数据是最新的 ???
                return;
            }
            ProductStdlibDO oldStdlibDO = stdlibDOMap.get(midStdlibId);
            // 根据id更新
            if (oldStdlibDO != null) {
                newStdlibDO.setId(oldStdlibDO.getId());
                ProductFlag productFlag = ProductFlag.ofStdlib(oldStdlibDO.getMultiFlag());
                // 中台同步是否跳过（不覆盖）
                if (productFlag.getMidSyncSkipped() == Boolean.TRUE) {
                    return;
                }
            }
            // 计算助记码、要素字段hash值
            newStdlibDO.calcMnemonicCodeAndKeyPointHash();
            // 图片（取前8个）
            MidPictureProResult pictureProResult = midStdlibPicMap.get("" + midStdlibId);
            newStdlibDO.assembleImages(pictureProResult, oldStdlibDO);

            midStdlibIdMap.put(midStdlibId, newStdlibDO);
        });
        // 中台标准库id去重后的数据（为啥中台id会有重复数据）
        List<ProductStdlibDO> stdlibList = midStdlibIdMap.values().stream().toList();

        // 六级分类字典更新
        syncCategoryData(stdlibList);
        // 字典更新
        syncDictData(stdlibList);

        // 保存标准库数据
        return productStdlibMapper.insertOrUpdateWithCheckUnique(stdlibList);
    }

    /**
     * 同步字典数据
     */
    private void syncDictData(List<ProductStdlibDO> stdlibList) {
        if (CollectionUtils.isEmpty(stdlibList)) {
            return;
        }
        Class<? extends ProductStdlibDO> stdlibClazz = stdlibList.getFirst().getClass();
        // 获取 DictFieldType 注解的字段
        Field[] fields = ReflectUtil.getFields(stdlibClazz, field -> field.isAnnotationPresent(DictFieldType.class));

        // 根据dictType 归档字典数据
        Map<String, Set<String>> dictList = new HashMap<>();
        stdlibList.forEach(stdlib -> {
            for (Field field : fields) {
                DictFieldType dictFieldType = field.getAnnotation(DictFieldType.class);
                if (dictFieldType == null) {
                    continue;
                }
                // 多值（businessScope）,根据间隔符（可能存在多个间隔符，比如中文和英文的逗号）分隔dictValue 
                String[] multiValueGap = Arrays.stream(dictFieldType.multiValueGap()).filter(StringUtils::isNotEmpty).toList().toArray(new String[0]);

                try {
                    Object dictValue = ReflectUtil.getFieldValue(stdlib, field);
                    if (dictValue == null) {
                        continue;
                    }
                    String dictType = dictFieldType.value();
                    String strDictValue = String.valueOf(dictValue);
                    
                    // 如果是多值字段且有分隔符
                    if (multiValueGap.length > 0) {
                        // 先用第一个分隔符分割
                        String[] values = strDictValue.split(multiValueGap[0]);
                        // 如果有多个分隔符，继续分割
                        if (multiValueGap.length > 1) {
                            values = Arrays.stream(values)
                                .flatMap(v -> Arrays.stream(v.split(String.join("|", Arrays.copyOfRange(multiValueGap, 1, multiValueGap.length)))))
                                .toArray(String[]::new);
                        }
                        // 添加所有分割后的值
                        Arrays.stream(values)
                            .filter(StringUtils::isNotEmpty)
                            .map(String::trim)
                            .forEach(v -> dictList.computeIfAbsent(dictType, k -> new HashSet<>()).add(v));
                    } else {
                        // 非多值字段直接添加
                        dictList.computeIfAbsent(dictType, k -> new HashSet<>()).add(strDictValue);
                    }
                } catch (Exception e) {
                    log.error("[syncDictData][字段:{} 字典同步失败!]", field.getName(), e);
                }
            }
        });

        // 字典更新
        Long tenantId = TenantConstant.DEFAULT_TENANT_ID;
        dictList.forEach((dictType, values) -> {
            try {
                DictDataSaveBatchDto dto = new DictDataSaveBatchDto()
                    .setTenantId(tenantId)
                    .setDictType(dictType)
                    .setValueLabelMap(values.stream().distinct()
                        .collect(Collectors.toMap(Function.identity(), Function.identity())));

                dictDataApi.saveOrUpdateDictData(dto);
            } catch (Exception e) {
                log.error("[syncDictData][dictType({}) values({}) 字典同步失败!] {}", dictType, values, e.getMessage(), e);
            }
        });
    }

    /**
     * 同步分类数据
     */
    private void syncCategoryData(List<ProductStdlibDO> stdlibList) {
        if (CollectionUtils.isEmpty(stdlibList)) {
            return;
        }

        // 合并去重
        List<ProductCategoryDO> list = stdlibList.stream().flatMap(stdlib -> stdlib.assembleCategoryList().stream()).distinct().toList();

        // 批量保存或更新分类
        if (!list.isEmpty()) {
            categoryService.batchSaveOrUpdate(list);
        }
    }

    /**
     * 更新商品标准库图片信息（以中台数据覆盖）
     *
     * @param midStdlibIdList
     */
    @Override
    public List<ProductStdlibDO> updateStdlibImageFromMid(List<Long> midStdlibIdList) {
        if (CollectionUtils.isEmpty(midStdlibIdList)) {
            return List.of();
        }
        // 去重
        midStdlibIdList = midStdlibIdList.stream().distinct().toList();
        // 查询中台标准库数据（图片）
        Map<String, MidPictureProResult> midStdlibPicMap = midStdlibService.findProPicture(midStdlibIdList)
            .stream().collect(Collectors.toMap(MidPictureProResult::getProductId, Function.identity(), (a, b) -> b));
        if (CollectionUtils.sizeIsEmpty(midStdlibPicMap)) {
            return List.of();
        }

        // 查询自建标准库数据
        Map<Long, ProductStdlibDO> stdlibDOMap = productStdlibMapper.listByMidStdlibId(midStdlibIdList).stream()
            .collect(Collectors.toMap(ProductStdlibDO::getMidStdlibId, Function.identity(), (a, b) -> b));

        // 转换为标准库数据
        List<ProductStdlibDO> stdlibList = midStdlibPicMap.keySet().stream().map(productId -> {
            long midStdlibId = NumberUtils.toLong(productId, -1);
            if (midStdlibId < 0) {
                return null;
            }
            MidPictureProResult pictureProResult = midStdlibPicMap.get(productId);
            if (pictureProResult == null) {
                return null;
            }
            ProductStdlibDO oldStdlibDO = stdlibDOMap.get(midStdlibId);
            if (oldStdlibDO == null) {
                return null;
            }

            ProductFlag productFlag = ProductFlag.ofStdlib(oldStdlibDO.getMultiFlag());
            // 中台同步是否跳过（不覆盖）
            if (productFlag.getMidSyncSkipped() == Boolean.TRUE) {
                return null;
            }

            // 根据id更新
            return new ProductStdlibDO()
                .setId(oldStdlibDO.getId())
                .setMidStdlibId(midStdlibId)
                .assembleImages(pictureProResult, oldStdlibDO);
        }).filter(Objects::nonNull).toList();
        // 保存标准库数据（只修改图片，不修改六要素）
        return productStdlibMapper.insertOrUpdateWithCheckUnique(stdlibList, true);
    }


    /**
     * 新增或更新商品标准库
     *
     * @param dto
     * @param stdlibUpdate
     */
    @Override
    public ProductStdlibDO saveOrUpdateStdlib(@Nonnull ProductInfoDto dto, boolean stdlibUpdate) {
        // 0. 构建标准库对象
        ProductStdlibDto stdlibDto = BeanUtils.toBean(dto, ProductStdlibDto.class);
        stdlibDto.setId(dto.getStdlibId());

        ProductStdlibDO exist;
        // 1. 如果有标准库ID,查询已有数据
        if (ObjectUtil.isNotNull(dto.getStdlibId())) {
            exist = validateProductStdlibExists(dto.getStdlibId());
        } else {
            // 根据六要素查询是否已存在数据
            exist = productStdlibMapper.uniqueQuery(dto);
        }

        // 2. 不存在则新增
        if (exist == null) {
            exist = ProductConvert.INSTANCE.ProductInfoDto2ProductStdlibDO(stdlibDto);
            exist = productStdlibMapper.insertOrUpdateWithCheckUnique(exist);
        } else {
            // 3. 更新非六要素属性
            dto.setStdlibId(exist.getId());
            dto.setMidStdlibId(Optional.ofNullable(dto.getMidStdlibId()).orElse(exist.getMidStdlibId()));
            exist = productStdlibMapper.updateWithCheckUnique(dto, exist, !stdlibUpdate);
        }

        // 4. 设置标准库商品信息
        if (exist != null) {
            dto.setStdlibId(exist.getId());
            dto.setMidStdlibId(exist.getMidStdlibId());
        }
        return exist;
    }




    private ProductStdlibDO validateProductStdlibExists(Long id) {
        ProductStdlibDO exist = productStdlibMapper.selectById(id);
        if (exist == null) {
            throw exception(PRODUCT_STDLIB_NOT_EXISTS);
        }
        return exist;
    }

    /**
     * 批量更新商品标准库信息
     *
     * @param vo
     */
    @Override
    @Transactional
    public int batchUpdateStdlib(@Nonnull StdlibProductUpdateReqVo vo) {
        if (CollectionUtils.isEmpty(vo.getIdList()) || (vo.getDisable() == null && vo.getMidSyncSkipped() == null)) {
            return 0;
        }
        // 查询标准库数据
        return productStdlibMapper.batchUpdateStdlib(vo.Vo2Dto());
    }

    /**
     * 查询六要素是否已存在标准库数据
     * @param dto
     * @return
     */
    public ProductStdlibDO uniqueQuery(@Nonnull ProductInfoDto dto) {
        return productStdlibMapper.uniqueQuery(dto);
    }


    // region 匹配中台标准库商品

    /**
     * 匹配中台标准库
     * @param tenantId
     * @param dtoList
     * @return 返回更新匹配商品信息
     */
    @Override
    public List<ProductInfoDto> matchProduct2MidStdlib(Long tenantId, List<ProductInfoDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return List.of();
        }

        // 商品pref分组
        Map<String, ProductInfoDto> prefMap = dtoList.stream()
            .collect(Collectors.toMap(ProductInfoDto::getPref, Function.identity(), (a, b) -> b));

        // 1. 调用中台匹配接口
        List<MidGeneralMatchProductNewDto> matchResult = midStdlibService.getGeneralMatchProductNew(dtoList);
        Set<String> unmatchedPrefSet = prefMap.keySet();

        List<ProductInfoDto> updateMatchList = Optional.ofNullable(matchResult).orElseGet(List::of).stream().map(mr -> {
            List<MidGeneralMatchProductNewDto> matchList = mr.getMatchList();
            if (CollectionUtils.isEmpty(matchList)) {
                return null;
            }
            // 匹配多条取第一个
            Long midStdlibId = Long.valueOf(matchList.getFirst().getProductId());
            ProductInfoDto dto = prefMap.get(mr.getOldBusinessCode());
            if (dto == null) {
                return null;
            }
            unmatchedPrefSet.remove(dto.getPref());
            log.info("匹配到中台标准库数据, tenantId: {}, pref: {}, stdlibId: {}, midStdlibId: {}", tenantId, mr.getOldBusinessCode(), dto.getStdlibId(), midStdlibId);

            // 更新商品状态（更新自建标准库 midStdlibId）
            return ProductInfoDto.builder()
                .id(dto.getId()).stdlibId(dto.getStdlibId()).midStdlibId(midStdlibId)
                .tenantId(tenantId).headTenantId(dto.getHeadTenantId()).build();
        }).filter(Objects::nonNull).toList();

        if (CollectionUtils.isNotEmpty(unmatchedPrefSet)) {
            log.info("匹配不到中台标准库数据, tenantId: {}, prefList: {}", tenantId, unmatchedPrefSet);
            midStdlibInteractiveProducer.sendDelayMessage4Report(new ArrayList<>(unmatchedPrefSet), 10);
        }

        return updateMatchList;
    }

    // endregion


    // region 提报商品中台
    /**
     * 提报商品中台
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductTransferRecordDO reportProduct2MidStdlib(ProductInfoDto dto) {
        Long tenantId = dto.getTenantId();

        String productPref = dto.getPref();
        ProductTransferRecordSaveReqVO transferRecord = new ProductTransferRecordSaveReqVO()
            .setProductPref(productPref)
            .setType(ProductTransferTypeEnum.REPORT_MID.code)
            .setSourceTenantId(dto.getTenantId())
            .setTargetTenantId(-1L)
            .setStatus(ProductTransferStatusEnum.INIT.code)
            .of(dto);

        // 1. 调用中台提报接口
        MidResponse2<List<MidGeneralProductPresentDto>> reportResp = midStdlibService.addProductPresent(dto);
        log.info("商品提报中台, tenantId: {}, pref: {}, resp: {}", tenantId, productPref, JSON.toJSONString(reportResp));

        // 2. 更新商品流转记录
        transferRecord.setResult(JSON.toJSONString(reportResp));
        if (reportResp == null || reportResp.isFailure()) {
            transferRecord.setStatus(ProductTransferStatusEnum.FAIL.code);
        }

        ProductTransferRecordDO record = transferRecordService.createProductTransferRecord(transferRecord);
        // 提报失败
        if (Objects.equals(ProductTransferStatusEnum.FAIL.code, record.getStatus())) {
            String errorMsg = ProductPresentRecordRespVO.parseErrorMsg(record.getResult());
            // throw exception(PRODUCT_PRESENT_FAILED, errorMsg);
            throw exception(PRODUCT_PRESENT_FAILED, errorMsg);
        }

        return record;
    }

    // endregion


    // region 标准库分页查询（中台 + 自建）
    @Override
    public PageResult<StdlibProductVo> getMixedStdlibProductPage(StdlibProductMixedPageQueryVo queryVo) {
        TenantDto tenant = tenantApi.getTenant();
        Long tenantId = tenant.getId();
        // 总部租户信息
        Long headTenantId = tenant.getHeadTenantId();
        try {
            // 泛化调用（中台标准库）
            PageResult<MidSaasWithMixSearchInfoDto> saasWithMixSearchInfoDto = midStdlibService.fuzzySearchPlus(queryVo, false);
            boolean isEmpty = CollectionUtils.isEmpty(saasWithMixSearchInfoDto.getList());
            List<StdlibProductVo> voList = isEmpty ? new ArrayList<>() : convertList(tenantId, saasWithMixSearchInfoDto.getList());
            ;
            long count = isEmpty ? 0L : saasWithMixSearchInfoDto.getTotal();

            // 查询自建标准库数据，合并分页
            return mergeStdlibProductPageNoMid(headTenantId, queryVo, new PageResult<>(voList, count));
        } catch (Exception e) {
            log.error("findStandLibraryPage 调用中台标准库信息查询异常: {}", e.getMessage(), e);
        }
        return PageResult.empty();
    }

    /**
     * 合并查询自建标准库分页数据（无中台标准库id）
     *
     * @param queryVo
     * @param pageResult
     * @return
     */
    private PageResult<StdlibProductVo> mergeStdlibProductPageNoMid(Long headTenantId, StdlibProductMixedPageQueryVo queryVo, PageResult<StdlibProductVo> pageResult) {
        if (ObjectUtils.isNotEmpty(queryVo.getMidStdlibId())) {
            return pageResult;
        }
        // 计算中台标准库分页数据，如果数据没有满一页，则继续查询自建标准库数据，合并分页数据和结果
        int pageSize = queryVo.getPageSize();
        int currentPage = queryVo.getPageNo();
        long count = pageResult.getTotal();
        int totalPage = (int) ((count - 1) / pageSize + 1);
        int remainSize = (int) (pageSize * totalPage - count);

        // 查询自建标准库
        StdlibProductPageQueryVo queryVo2 = BeanUtils.toBean(queryVo, StdlibProductPageQueryVo.class);
        queryVo2.setMixedQuery(queryVo.getProductName());
        int startPageNo = Math.max(1, currentPage - totalPage);
        queryVo2.setPageNo(startPageNo);
        // 查询自建标准库数据
        PageResult<ProductStdlibDO> stdlibDOPageResult = productStdlibMapper.pageQuery(queryVo2, false);
        if (stdlibDOPageResult == null || CollectionUtils.isEmpty(stdlibDOPageResult.getList())) {
            return pageResult;
        }

        // 自建数据分页逻辑
        count += stdlibDOPageResult.getTotal();
        List<ProductStdlibDO> list2 = stdlibDOPageResult.getList();

        // 全部查自建数据
        if (currentPage > totalPage) {
            // 有补页数据，需要继续往下查询
            if (remainSize > 0) {
                // 查询下一页数据
                queryVo2.setPageNo(startPageNo + 1);
                PageResult<ProductStdlibDO> stdlibDOPageResult2 = productStdlibMapper.pageQuery(queryVo2, false);
                if (stdlibDOPageResult2 != null && CollectionUtils.isNotEmpty(stdlibDOPageResult2.getList())) {
                    list2.addAll(stdlibDOPageResult2.getList());
                }
            }
            // 跳过补页数据，取pageSize条数据
            list2 = list2.stream().skip(remainSize).limit(pageSize).toList();
        } else {
            // 部分查自建数据 - 补页数据，取剩余的数据
            list2 = list2.stream().limit(remainSize).toList();
        }
        // 合并数据
        List<StdlibProductVo> voList2 = convertList2(headTenantId, list2);
        List<StdlibProductVo> union = CollUtil.unionAll(pageResult.getList(), voList2);

        return new PageResult<>(union, count);
    }

    /**
     * 自建标准库数据转换 - 组装商品id
     *
     * @param headTenantId
     * @param itemList
     * @return
     */
    private List<StdlibProductVo> convertList2(Long headTenantId, List<ProductStdlibDO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return List.of();
        }
        // 查询组装商品数据
        List<Long> stdlibIds = itemList.stream().map(ProductStdlibDO::getId).toList();
        Map<Long, ProductInfoDO> productMap = productInfoMapper.listByStdlibId(headTenantId, stdlibIds).stream()
            .collect(Collectors.toMap(ProductInfoDO::getStdlibId, Function.identity(), (a, b) -> b));

        return itemList.stream().map(i -> {
            StdlibProductVo vo = BeanUtils.toBean(i, StdlibProductVo.class);
            vo.setMidStdlibId(null);
            vo.setStdlibId(i.getId());
            Optional.ofNullable(productMap.get(i.getId())).ifPresent(productInfoDO -> {
                vo.setProductId(productInfoDO.getId());
                vo.setShowPref(productInfoDO.getShowPref());
            });
            return vo;
        }).toList();
    }

    /**
     * 中台标准库数据转换 - 组装自建标准库id - 组装商品id
     *
     * @param headTenantId
     * @param itemList
     * @return
     */
    private List<StdlibProductVo> convertList(Long headTenantId, List<? extends MidSaasWithMixSearchInfoDto> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return List.of();
        }
        // 循环转换
        List<StdlibProductVo> list = itemList.stream().map(StdlibProductVo::of).toList();
        // 查询组装自建标准库数据
        List<Long> midStdlibIds = list.stream().map(StdlibProductVo::getMidStdlibId).toList();
        List<ProductStdlibDO> stdlibDOList = productStdlibMapper.listByMidStdlibId(midStdlibIds);
        Map<Long, ProductStdlibDO> stdlibMap = stdlibDOList.stream()
            .collect(Collectors.toMap(ProductStdlibDO::getMidStdlibId, Function.identity(), (a, b) -> b));

        // 查询组装商品数据
        List<Long> stdlibIds = stdlibDOList.stream().map(ProductStdlibDO::getId).toList();
        Map<Long, ProductInfoDO> productMap = productInfoMapper.listByStdlibId(headTenantId, stdlibIds).stream()
            .collect(Collectors.toMap(ProductInfoDO::getStdlibId, Function.identity(), (a, b) -> b));

        return list.stream().peek(vo -> {
            Optional.ofNullable(stdlibMap.get(vo.getMidStdlibId())).ifPresent(stdlibDO -> {
                vo.setStdlibId(stdlibDO.getId());
                Optional.ofNullable(productMap.get(stdlibDO.getId())).ifPresent(productInfoDO -> {
                    vo.setProductId(productInfoDO.getId());
                    vo.setShowPref(productInfoDO.getShowPref());
                });
            });
        }).toList();
    }

    // endregion

    // region 商品标准库分页查询（自建）

    /**
     * 商品标准库分页查询（自建）
     *
     * @param queryVo
     * @return
     */
    @Override
    public PageResult<ProductStdlibDto> getSelfStdlibProductPage(StdlibProductPageQueryVo queryVo) {
        if (queryVo.getStatus() == null) {
            queryVo.setStatus(ProductStdlibStatusEnum.USING.code);
        }
        ProductFlag multiFlag = Optional.ofNullable(queryVo.getMultiFlag()).orElseGet(ProductFlag::new);
        // 过滤中台未停用商品
        if (multiFlag.getMidDeactivated() == null) {
            queryVo.setMultiFlag(multiFlag.setMidDeactivated(false));
        }
        PageResult<ProductStdlibDO> pageResult = productStdlibMapper.pageQuery(queryVo, true);
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        // 补充关联商品数据
        Long headTenantId = tenantApi.getTenant().getHeadTenantId();
        // 查询组装商品数据
        List<Long> stdlibIds = pageResult.getList().stream().map(ProductStdlibDO::getId).toList();
        Map<Long, ProductInfoDO> productMap = productInfoMapper.listByStdlibId(headTenantId, stdlibIds).stream()
            .collect(Collectors.toMap(ProductInfoDO::getStdlibId, Function.identity(), (a, b) -> b));

        List<ProductStdlibDto> stdlibDtoList = ProductConvert.INSTANCE.ProductStdlibDO2DtoList(pageResult.getList());
        stdlibDtoList.forEach(stdlibDto -> {
            Optional.ofNullable(productMap.get(stdlibDto.getId())).ifPresent(productInfoDO -> {
                stdlibDto.setProductId(productInfoDO.getId());
                stdlibDto.setProductPref(productInfoDO.getPref());
                stdlibDto.setShowPref(productInfoDO.getShowPref());
            });
        });

        return new PageResult<>(stdlibDtoList, pageResult.getTotal());
    }

    // endregion

    // region 商品标准库信息

    /**
     * 商品标准库信息
     *
     * @param id
     * @return
     */
    @Override
    public ProductStdlibDto getStdlibProduct(Long id) {
        ProductStdlibDO stdlibDO = productStdlibMapper.selectById(id);
        return ProductConvert.INSTANCE.ProductStdlibDO2Dto(stdlibDO);
    }

    // endregion

    // region 荷叶问诊搜索药品

    /**
     * 查询自建标准库中去重后的商品通用名列表
     *
     * @param searchDto 查询条件
     * @param limit     返回条数限制
     * @return 去重后的商品通用名列表
     */
    @Override
    public List<String> listDistinctCommonName(StdlibProductSearchDto searchDto, int limit) {
        limit = Math.max(1, limit);
        // 1. 组装默认属性
        searchDto.assembleAttrDefault();

        // 2. 执行查询并返回结果
        List<String> distinctCommonNameList = new ArrayList<>();

        for (int i = 1; i < 10; i++) {
            // 查询符合条件数据
            List<ProductStdlibDO> productStdlibDOList = productStdlibMapper.selectDistinctCommonName(searchDto, limit, (i - 1) * limit);
            if (CollUtil.isEmpty(productStdlibDOList)) {
                break;
            }
            // 数据库查询原始数据长度
            int originalSize = productStdlibDOList.size();
            // 过滤掉限制范围内的药品 , 数据库内的BusinessScope是由,拼接的
            if (CollUtil.isNotEmpty(searchDto.getNeedExcludeBusinessScopeList())) {
                productStdlibDOList = productStdlibDOList.stream()
                    .filter(s -> {
                        // 如果 BusinessScope 为空或 null，直接保留
                        if (StringUtils.isBlank(s.getBusinessScope())) {
                            return true;
                        }
                        // 拆分并判断是否有任意一个在排除列表中
                        return Arrays.stream(s.getBusinessScope().split(","))
                            .noneMatch(searchDto.getNeedExcludeBusinessScopeList()::contains);
                    }).collect(Collectors.toList());
            }
            if (CollUtil.isEmpty(productStdlibDOList)) {
                continue;
            }
            distinctCommonNameList.addAll(productStdlibDOList.stream().map(ProductStdlibDO::getCommonName).toList());
            if (distinctCommonNameList.size() >= limit || originalSize < limit) {
                break;
            }
        }

        return distinctCommonNameList;
    }

    /**
     * 查询自建标准库商品（条形码传0，不根据条形码查询）
     *
     * @param barcode
     * @return 去重后的商品品牌列表
     */
    @Override
    public List<ProductStdlibDto> searchStdlibByBarcode(String barcode) {
        // 条形码传0，不根据条形码查询
        if (StringUtils.isEmpty(barcode) || barcode.equals("0")) {
            return List.of();
        }
        List<ProductStdlibDto> dtos = searchStdlibProductList(new StdlibProductSearchDto().setBarcode(barcode), 1);
        if (CollectionUtils.isEmpty(dtos)) {
            return List.of();
        }
        return dtos;
    }

    /**
     * 查询自建标准库商品
     *
     * @param searchDto 查询条件
     * @param limit     返回条数限制
     * @return 去重后的商品通用名列表
     */
    @Override
    public List<ProductStdlibDto> searchStdlibProductList(StdlibProductSearchDto searchDto, int limit) {
        limit = Math.max(1, limit);
        // 1. 组装默认属性
        searchDto.assembleAttrDefault();

        // 2. 执行查询并返回结果
        List<ProductStdlibDO> productStdlibDOS = productStdlibMapper.searchStdlibProductList(searchDto, limit, 0);

        return ProductConvert.INSTANCE.ProductStdlibDO2DtoList(productStdlibDOS);
    }

    /**
     * 查询自建标准库商品（根据通用名+规则分组去重，组内随机取一条）
     *
     * @param searchDto 查询条件
     * @param limit     返回条数限制
     * @return 去重后的商品品牌列表
     */
    @Override
    public List<ProductStdlibDto> searchStdlibProductListGroupByNameSpec(StdlibProductSearchDto searchDto, int limit) {
        limit = Math.max(1, limit);
        // 1. 组装默认属性
        searchDto.assembleAttrDefault();

        // 2. 执行查询并返回结果 循环查到limit条 - 手动分组
        // productStdlibMapper.searchStdlibProductListGroupByNameSpec(searchDto, limit);
        Table<String, String, ProductStdlibDO> stdlibDOTable = new RowKeyTable<>(true);
        for (int i = 1; i < 10; i++) {
            List<ProductStdlibDO> searched = productStdlibMapper.searchStdlibProductList(searchDto, limit, (i - 1) * limit);
            if (CollUtil.isEmpty(searched)) {
                break;
            }
            // 数据库查询原始数据长度
            int originalSize = searched.size();
            // 过滤掉限制范围内的药品 , 数据库内的BusinessScope是由,拼接的
            if (CollUtil.isNotEmpty(searchDto.getNeedExcludeBusinessScopeList())) {
                searched = searched.stream()
                    .filter(s -> {
                        // 如果 BusinessScope 为空或 null，直接保留
                        if (StringUtils.isBlank(s.getBusinessScope())) {
                            return true;
                        }
                        // 拆分并判断是否有任意一个在排除列表中
                        return Arrays.stream(s.getBusinessScope().split(","))
                            .noneMatch(searchDto.getNeedExcludeBusinessScopeList()::contains);
                    }).collect(Collectors.toList());
            }
            if (CollUtil.isEmpty(searched)) {
                continue;
            }
            // 当为中药时 , 需要根据通用名去重
            if (ProductSpuCategoryEnum.TRADITIONAL_CHINESE_MEDICINE.desc.equals(searchDto.getSpuCategory())) {
                searched.forEach(s -> {
                    if (!stdlibDOTable.contains(s.getCommonName(), "")) {
                        stdlibDOTable.put(s.getCommonName(), "", s);
                    }
                });
                // 当为西药时 , 需要根据通用名+规格去重
            } else {
                searched.forEach(s -> {
                    if (!stdlibDOTable.contains(s.getCommonName(), s.getSpec())) {
                        stdlibDOTable.put(s.getCommonName(), s.getSpec(), s);
                    }
                });
            }
            // 如果分组数量已经满足
            if (stdlibDOTable.size() >= limit || originalSize < limit) {
                break;
            }
        }
        // table 和数据库分页查询的顺序一致
        List<ProductStdlibDO> stdlibDOList = stdlibDOTable.values().stream().toList();
        return ProductConvert.INSTANCE.ProductStdlibDO2DtoList(stdlibDOList);
    }

    // endregion
}
