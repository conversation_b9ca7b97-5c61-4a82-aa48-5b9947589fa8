<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.gsp.ProductPriceAdjustmentDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="record_pref" property="recordPref" jdbcType="VARCHAR"/>
    <result column="applicable_tenant_id" property="applicableTenantId" jdbcType="BIGINT"/>
    <result column="product_pref" property="productPref" jdbcType="VARCHAR"/>
    <result column="show_pref" property="showPref" jdbcType="VARCHAR"/>
    <result column="old_retail_price" property="oldRetailPrice" jdbcType="DECIMAL"/>
    <result column="old_member_price" property="oldMemberPrice" jdbcType="DECIMAL"/>
    <result column="new_retail_price" property="newRetailPrice" jdbcType="DECIMAL"/>
    <result column="new_member_price" property="newMemberPrice" jdbcType="DECIMAL"/>
    <result column="remark" property="remark" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    <result column="creator" property="creator" jdbcType="VARCHAR"/>
    <result column="updater" property="updater" jdbcType="VARCHAR"/>
    <result column="deleted" property="deleted" jdbcType="BIT"/>
  </resultMap>

  <sql id="Where_Search_Detail_AS_dtl">
    <if test="req.recordPref != null">
      AND dtl.record_pref = #{req.recordPref}
    </if>
    <if test="req.applicableTenantId != null">
      AND dtl.applicable_tenant_id = #{req.applicableTenantId}
    </if>
    <if test="req.applicableTenantIdList != null and req.applicableTenantIdList.size() > 0">
      AND dtl.applicable_tenant_id IN
      <foreach collection="req.applicableTenantIdList" item="applicableTenantId" open="(" close=")" separator=",">
        #{applicableTenantId}
      </foreach>
    </if>
    <if test="req.productPref != null">
      AND dtl.product_pref = #{req.productPref}
    </if>
    <if test="req.productPrefList != null and req.productPrefList.size() > 0">
      AND dtl.product_pref IN
      <foreach collection="req.productPrefList" item="productPref" open="(" close=")" separator=",">
        #{productPref}
      </foreach>
    </if>
    <if test="req.createTime != null and req.createTime.length > 0">
      AND create_time BETWEEN #{req.createTime[0],javaType=java.time.LocalDateTime} AND
      #{req.createTime[1],javaType=java.time.LocalDateTime}
    </if>
  </sql>


  <select id="selectPageWithProductInfo" resultType="com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentDetailRespVO">

    SELECT dtl.*,
      p.pref,
      p.show_pref,
      p.stdlib_id,
      p.mnemonic_code,
      p.common_name,
      p.brand_name,
      p.spec,
      p.barcode,
      p.manufacturer,
      p.approval_number
    FROM saas_product_quality_change_detail dtl
      JOIN saas_product_info p ON p.pref = dtl.item_pref AND p.deleted = 0
    WHERE dtl.deleted = 0 AND p.deleted = 0
    <include refid="Where_Search_Detail_AS_dtl" />
    <if test="req.mixedQuery != null and req.mixedQuery != ''">
      AND match(p.show_pref, p.common_name, p.brand_name, p.barcode, p.mnemonic_code, p.approval_number, p.manufacturer) against( #{req.mixedQuery} in BOOLEAN MODE )
    </if>
    ORDER BY dtl.id DESC
  </select>
</mapper>