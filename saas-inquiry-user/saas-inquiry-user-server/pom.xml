<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-user</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <artifactId>saas-inquiry-user-server</artifactId>

  <description>inquiry-user 模块 server，提供核心业务支撑服务 例如说：用户、部门、权限、数据字典等等</description>


  <dependencies>
    <!-- Nacos Discovery Starter -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-server</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-api</artifactId>
      <version>${revision}</version>
      <exclusions>
        <exclusion>
          <groupId>com.xyy.saas</groupId>
          <artifactId>biz-soa-starter</artifactId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pharmacist-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-system-biz</artifactId>
      <version>${yudao.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-member-biz</artifactId>
      <version>${yudao.version}</version>
    </dependency>


    <dependency>
      <groupId>com.xyy.common</groupId>
      <artifactId>xyy-common-dubbo-client</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
      <version>0.0.1-SNAPSHOT</version>
    </dependency>
    <!-- Token生成与解析-->
    <!--<dependency>-->
    <!--    <groupId>io.jsonwebtoken</groupId>-->
    <!--    <artifactId>jjwt</artifactId>-->
    <!--    <version>0.9.1</version>-->
    <!--</dependency>-->
    <!-- JWT (higress官方JWT插件指定) -->
    <dependency>
      <groupId>org.bitbucket.b_c</groupId>
      <artifactId>jose4j</artifactId>
      <version>0.7.0</version>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-infra-biz</artifactId>
      <version>${yudao.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>spring-boot-admin-starter-server</artifactId>
          <groupId>de.codecentric</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yudao-spring-boot-starter-monitor</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
  </dependencies>

  <build>
    <!--  打包依赖到jar包中  -->
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>