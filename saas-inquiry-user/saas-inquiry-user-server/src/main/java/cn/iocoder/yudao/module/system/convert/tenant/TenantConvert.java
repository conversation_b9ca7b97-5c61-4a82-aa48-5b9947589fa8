package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.enums.AreaTypeEnum;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantAndPackageExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantCertificateTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateMessageDto;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantConvert {

    TenantConvert INSTANCE = Mappers.getMapper(TenantConvert.class);

    default UserSaveReqVO convert02(TenantSaveReqVO bean) {
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setUsername(bean.getUsername());
        reqVO.setPassword(bean.getPassword());
        reqVO.setNickname(bean.getContactName()).setMobile(bean.getContactMobile());
        return reqVO;
    }

    default List<TenantSimpleRespVO> convert2SimpleRespVO(List<TenantDO> tenantDOS) {
        if (tenantDOS == null) {
            return null;
        }
        return tenantDOS.stream().map(t -> {
            TenantSimpleRespVO respVO = new TenantSimpleRespVO();
            respVO.setId(t.getId());
            respVO.setType(t.getType());
            respVO.setName(t.getName());
            respVO.setAddress(Stream.of(t.getProvince(), t.getCity(), t.getArea(), t.getAddress()).filter(StringUtils::isNotBlank).collect(Collectors.joining()));
            return respVO;
        }).collect(Collectors.toList());


    }

    TenantRespVO convertDo2Vo(TenantDO tenant);

    @Mapping(target = "type", expression = "java(com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum.fromCode(tenantDO.getType()))")
    @Mapping(target = "headTenantId", expression = "java(tenantDO.getHeadTenantId() == null ? tenantDO.getId() : tenantDO.getHeadTenantId())")
    TenantDto convertDto(TenantDO tenantDO);

    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getMdPref())")
    TenantDO convertInitDo(TenantSaveReqVO createReqVO);

    List<TenantDto> convertDtos(List<TenantDO> tenantDOList);


    @Mapping(target = "id", source = "contactUserId")
    @Mapping(target = "username", source = "contactMobile")
    @Mapping(target = "mobile", source = "contactMobile")
    @Mapping(target = "nickname", source = "contactName")
    @Mapping(target = "status", ignore = true)
    UserSaveReqVO convertUser(TenantSaveReqVO updateReqVO);

    List<TenantRespVO> convertDo2Vos(List<TenantDO> records);


    TenantPageReqVO convert(TenantReqDto tenantReqDto);

    List<TenantRespDto> convertRespDtos(List<TenantRespVO> list);

    TenantInfoUpdateMessageDto convertUpdateInfo(TenantDO tenantDO);

    default TenantSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO, Map<String, TenantDO> tenantDOMap, List<TenantCertificateRespVO> certs) {

        int accountCount = 100;

        TenantSaveReqVO tenantSaveReqVO = StringUtils.isBlank(excelVO.getPref()) ? convertImportVo(excelVO) : convertDo2SaveVo(tenantDOMap.get(excelVO.getPref()));
        tenantSaveReqVO.setHeadTenantId(StringUtils.isBlank(excelVO.getHeadTenantPref()) ? null : tenantDOMap.get(excelVO.getHeadTenantPref()).getId());

        tenantSaveReqVO.setWzAccountCount(accountCount);
        tenantSaveReqVO.setZhlAccountCount(accountCount);

        if (StringUtils.isNotBlank(excelVO.getBizType())) {
            tenantSaveReqVO.setWzBizTypeStatus(StringUtils.contains(excelVO.getBizType(), BizTypeEnum.HYWZ.getCode() + "") ? CommonStatusEnum.ENABLE.getStatus() : CommonStatusEnum.DISABLE.getStatus());
            tenantSaveReqVO.setZhlBizTypeStatus(StringUtils.contains(excelVO.getBizType(), BizTypeEnum.ZHL.getCode() + "") ? CommonStatusEnum.ENABLE.getStatus() : CommonStatusEnum.DISABLE.getStatus());
        }

        // 修改
        if (StringUtils.isNotBlank(excelVO.getPref())) {
            TenantDO tenantDO = tenantDOMap.get(excelVO.getPref());
            copyNonNull(excelVO, tenantSaveReqVO);
            // 非连锁门店，设置 headTenantId 为 null
            if (excelVO.type() != null && !Objects.equals(excelVO.type(), TenantTypeEnum.CHAIN_STORE.getCode())) {
                tenantSaveReqVO.setHeadTenantId(null);
            } else {
                tenantSaveReqVO.setHeadTenantId(StringUtils.isBlank(excelVO.getHeadTenantPref()) ? tenantDO.getHeadTenantId() : tenantDOMap.get(excelVO.getHeadTenantPref()).getId());
            }
            tenantSaveReqVO.setWzAccountCount(CommonStatusEnum.isEnable(tenantSaveReqVO.getWzBizTypeStatus()) && CommonStatusEnum.isDisable(tenantDO.getWzBizTypeStatus())
                ? accountCount : tenantDO.getWzAccountCount());
            tenantSaveReqVO.setZhlAccountCount(CommonStatusEnum.isEnable(tenantSaveReqVO.getZhlBizTypeStatus()) && CommonStatusEnum.isDisable(tenantDO.getZhlBizTypeStatus())
                ? accountCount : tenantDO.getZhlAccountCount());
        }

        if (StringUtils.isNotBlank(excelVO.getProvince())) {
            Area p1 = AreaUtils.getByTypeName(AreaTypeEnum.PROVINCE, excelVO.getProvince());
            if (p1 != null) {
                tenantSaveReqVO.setProvinceCode(p1.getId().toString());
            }
        }
        if (StringUtils.isNotBlank(excelVO.getCity())) {
            Area p2 = AreaUtils.getByTypeName(AreaTypeEnum.CITY, excelVO.getCity());
            if (p2 != null) {
                tenantSaveReqVO.setCityCode(p2.getId().toString());

                if (StringUtils.isNotBlank(excelVO.getArea())) {
                    p2.getChildren().stream().filter(p3 -> StringUtils.equalsIgnoreCase(p3.getName(), excelVO.getArea()) && Objects.equals(p3.getType(), AreaTypeEnum.DISTRICT.getType())).findFirst().ifPresent(p3 -> {
                        tenantSaveReqVO.setAreaCode(p3.getId().toString());
                    });
                }
            }
        }

        // 处理配置参数
        List<TenantParamConfigDto> paramConfigDtos = Stream.of(StringUtils.isBlank(excelVO.getInquiryService()) ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_SERVER.getType())
                .paramValue(excelVO.getInquiryService())
                .build(),
            StringUtils.isBlank(excelVO.getPrescriptionDateFormat()) ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_PRES_DATE_TYPE.getType())
                .paramValue(excelVO.getPrescriptionDateFormat())
                .build(),
            StringUtils.isBlank(excelVO.getReviewType()) ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_TYPE.getType())
                .paramValue(excelVO.getReviewType())
                .build()
        ).filter(Objects::nonNull).toList();
        tenantSaveReqVO.setInquiryParamConfigs(paramConfigDtos);

        // 处理资质
        if (CollUtil.isEmpty(certs)) {
            tenantSaveReqVO.setCertificates(List.of(
                TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                    .certificateType(TenantCertificateTypeEnum.YYZJ.getType())
                    .certificateName(excelVO.getBusinessLicenseName())
                    .certificateNo(excelVO.getBusinessLicenseNumber())
                    .build(),
                TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                    .certificateType(TenantCertificateTypeEnum.JYXK.getType())
                    .certificateName(TenantCertificateTypeEnum.JYXK.getDescription())
                    .certificateNo(excelVO.getDrugBusinessLicenseNumber())
                    .registerTime(excelVO.drugBusinessRegistrationDate())
                    .validTime(excelVO.drugBusinessLicenseExpiry())
                    .build(),
                TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                    .certificateType(TenantCertificateTypeEnum.JYZLGL.getType())
                    .certificateName(TenantCertificateTypeEnum.JYZLGL.getDescription())
                    .certificateNo(excelVO.getGspLicenseNumber())
                    .build()
            ));
        } else {
            for (TenantCertificateRespVO cert : certs) {
                if (Objects.equals(cert.getCertificateType(), TenantCertificateTypeEnum.YYZJ.getType())) {
                    cert.setCertificateName(excelVO.getBusinessLicenseName());
                    cert.setCertificateNo(excelVO.getBusinessLicenseNumber());
                }
                if (Objects.equals(cert.getCertificateType(), TenantCertificateTypeEnum.JYXK.getType())) {
                    cert.setCertificateNo(excelVO.getDrugBusinessLicenseNumber());
                    cert.setRegisterTime(excelVO.drugBusinessRegistrationDate());
                    cert.setValidTime(excelVO.drugBusinessLicenseExpiry());
                }
                if (Objects.equals(cert.getCertificateType(), TenantCertificateTypeEnum.JYZLGL.getType())) {
                    cert.setCertificateNo(excelVO.getGspLicenseNumber());
                }
            }
            tenantSaveReqVO.setCertificates(TenantCertificateConvert.INSTANCE.convert2Do(certs));
        }

        return tenantSaveReqVO;

    }

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void copyNonNull(TenantAndPackageExcelVO source, @MappingTarget TenantSaveReqVO target);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void copyNonNull(TenantDO tenantDO, @MappingTarget TenantSaveReqVO target);

    TenantSaveReqVO convertDo2SaveVo(TenantDO tenantDO);

    @Mapping(target = "username", source = "contactMobile")
    TenantSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO);

    TenantInfoVO convertDto2Vo(TenantDO tenantDO);

    default TenantSaveReqVO convertMigrationSaveVo(TenantDO tenant, MigrationDrugStoreRespDto dto, List<TenantCertificateRespVO> certificates) {
        TenantSaveReqVO saveReqVO = convertMigrationSaveVo(dto);
        if (tenant == null) {
            return saveReqVO;
        }

        copyNonNull(tenant, saveReqVO);
        return saveReqVO;
    }

    default TenantSaveReqVO convertMigrationSaveVo(MigrationDrugStoreRespDto dto) {
        TenantSaveReqVO tenantSaveReqVO = new TenantSaveReqVO();
        tenantSaveReqVO.setType(TenantTypeEnum.SINGLE_STORE.getCode());
        tenantSaveReqVO.setStatus(CommonStatusEnum.DISABLE.getStatus());

        tenantSaveReqVO.setExt(TenantExtDto.builder().organSign(dto.getOrganSign()).hyId(dto.getHyid()).build());

        tenantSaveReqVO.setWzAccountCount(100);
        tenantSaveReqVO.setZhlAccountCount(100);
        tenantSaveReqVO.setWzBizTypeStatus(CommonStatusEnum.ENABLE.getStatus());
        tenantSaveReqVO.setName(dto.getDrugstoreName());
        tenantSaveReqVO.setContactName(dto.getManagerName());
        tenantSaveReqVO.setContactMobile(dto.getContactPhone());
        tenantSaveReqVO.setSmartfaceOrganSign(dto.getSmartfaceOrganSign());
        tenantSaveReqVO.setAddress(dto.getAddress());
        // 转换省市区
        tenantSaveReqVO.setProvince(dto.getProvince());
        tenantSaveReqVO.setCity(dto.getCity());
        tenantSaveReqVO.setArea(dto.getArea());

        if (StringUtils.isNotBlank(dto.getProvince())) {
            Optional.ofNullable(AreaUtils.getByTypeName(AreaTypeEnum.PROVINCE, dto.getProvince())).ifPresent(p1 -> {
                tenantSaveReqVO.setProvinceCode(p1.getId().toString());

                if (StringUtils.isNotBlank(dto.getCity())) {
                    p1.getChildren().stream().filter(p2 -> StringUtils.equalsIgnoreCase(p2.getName(), dto.getCity()) && Objects.equals(p2.getType(), AreaTypeEnum.CITY.getType())).findFirst().ifPresent(p2 -> {
                        tenantSaveReqVO.setCityCode(p2.getId().toString());

                        if (StringUtils.isNotBlank(dto.getArea())) {
                            p2.getChildren().stream().filter(p3 -> StringUtils.equalsIgnoreCase(p3.getName(), dto.getArea()) && Objects.equals(p3.getType(), AreaTypeEnum.DISTRICT.getType())).findFirst().ifPresent(p3 -> {
                                tenantSaveReqVO.setAreaCode(p3.getId().toString());
                            });
                        }
                    });
                }
            });
        }

        tenantSaveReqVO.setBusinessLicenseName(dto.getBusinessLicenseName());
        tenantSaveReqVO.setBusinessLicenseNumber(dto.getBusinessLicenseNumber());

        // 处理配置参数
        List<TenantParamConfigDto> paramConfigDtos = Stream.of(TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_SERVER.getType())
                .paramValue(Objects.equals(dto.getStatus(), 1) ? CommonStatusEnum.ENABLE.getStatus() + "" : CommonStatusEnum.DISABLE.getStatus() + "")
                .build(),
            dto.getDateType() == null ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_PRES_DATE_TYPE.getType())
                .paramValue(Objects.equals(dto.getDateType(), 0) ? "1" : dto.getDateType().toString())
                .build(),
            dto.getServerType() == null ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_TYPE.getType())
                .paramValue(Objects.equals(dto.getServerType(), (byte) 2) ? PrescriptionAuditTypeEnum.OFFLINE.getStatusCode() + "" : PrescriptionAuditTypeEnum.DRUGSTORE.getStatusCode() + "")
                .build(),
            dto.getServerType() == null ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_WESTERN_MEDICINE_BRING.getType())
                .paramValue(Objects.equals(dto.getWesternMedicineBring(), 1) ? CommonStatusEnum.ENABLE.getStatus() + "" : CommonStatusEnum.DISABLE.getStatus() + "")
                .build(),
            dto.getServerType() == null ? null : TenantParamConfigDto.builder().tenantId(tenantSaveReqVO.getId())
                .paramType(TenantParamConfigTypeEnum.INQUIRY_CHINESE_MEDICINE_BRING.getType())
                .paramValue(Objects.equals(dto.getChineseMedicineBring(), 1) ? CommonStatusEnum.ENABLE.getStatus() + "" : CommonStatusEnum.DISABLE.getStatus() + "")
                .build()
        ).filter(Objects::nonNull).toList();
        tenantSaveReqVO.setInquiryParamConfigs(paramConfigDtos);

        // 处理资质
        tenantSaveReqVO.setCertificates(List.of(
            TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                .certificateType(TenantCertificateTypeEnum.YYZJ.getType())
                .certificateName(dto.getBusinessLicenseName())
                .certificateNo(dto.getBusinessLicenseNumber())
                .certificateImgUrls(StringUtils.isBlank(dto.getBusinessLicenseImg()) ? null : List.of(StringUtils.split(dto.getBusinessLicenseImg(), "\\|")))
                .build(),
            TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                .certificateType(TenantCertificateTypeEnum.JYXK.getType())
                .certificateName(TenantCertificateTypeEnum.JYXK.getDescription())
                .certificateNo(dto.getStoreNo())
                .registerTime(dto.getPharmaceuticalTradingLicenseRegistryTime() == null ? null : dto.getPharmaceuticalTradingLicenseRegistryTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                .validTime(dto.getPharmaceuticalTradingLicenseValidityPeriod() == null ? null : dto.getPharmaceuticalTradingLicenseValidityPeriod().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                .certificateImgUrls(StringUtils.isBlank(dto.getPharmaceuticalTradingLicenseImg()) ? null : List.of(StringUtils.split(dto.getPharmaceuticalTradingLicenseImg(), "\\|")))
                .build(),
            TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                .certificateType(TenantCertificateTypeEnum.JYZLGL.getType())
                .certificateName(TenantCertificateTypeEnum.JYZLGL.getDescription())
                .certificateNo(dto.getQualityManagementLicenseNumber())
                .certificateImgUrls(StringUtils.isBlank(dto.getQualityManagementLicenseImg()) ? null : List.of(StringUtils.split(dto.getQualityManagementLicenseImg(), "\\|")))
                .build(),

            TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                .certificateType(TenantCertificateTypeEnum.WZFWHT.getType())
                .certificateName(TenantCertificateTypeEnum.WZFWHT.getDescription())
                .certificateImgUrls(StringUtils.isBlank(dto.getLzManagementLicense()) ? null : List.of(StringUtils.split(dto.getLzManagementLicense(), "\\|")))
                .build(),
            TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                .certificateType(TenantCertificateTypeEnum.BAHTBMXY.getType())
                .certificateName(TenantCertificateTypeEnum.BAHTBMXY.getDescription())
                .certificateImgUrls(StringUtils.isBlank(dto.getRecordContractsAndConfidentialityAgreements()) ? null : List.of(StringUtils.split(dto.getRecordContractsAndConfidentialityAgreements(), "\\|")))
                .build(),
            TenantCertificateSaveReqVO.builder().tenantId(tenantSaveReqVO.getId())
                .certificateType(TenantCertificateTypeEnum.YCSFBA.getType())
                .certificateName(TenantCertificateTypeEnum.YCSFBA.getDescription())
                .certificateImgUrls(StringUtils.isBlank(dto.getRemoteAuditArchiveDocuments()) ? null : List.of(StringUtils.split(dto.getRemoteAuditArchiveDocuments(), "\\|")))
                .build()
        ));

        return tenantSaveReqVO;
    }
}
