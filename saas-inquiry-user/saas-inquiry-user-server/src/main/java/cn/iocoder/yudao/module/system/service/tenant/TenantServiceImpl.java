package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.CHANGE_MOBILE_FAIL_LOCKED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_ADMIN_MOBILE_NOT_EXIST;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_ADMIN_NOT_OPERATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BUSINESS_LICENSE_NAME_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BUSINESS_LICENSE_NUMBER_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_CAN_NOT_UPDATE_SYSTEM;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_HEAD_DISABLE_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_HEAD_STORE_TYPE_CHANGE_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_HEAD_TYPE_CHANGE_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_NAME_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TYPE_CAN_NOT_RELATION_CHAIN;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TYPE_CHAIN_STORE_CAN_NOT_DELETE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TYPE_MUST_RELATION_CHAIN;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TYPE_UN_SUPPORT;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_CREATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_CREATE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_DELETE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_DELETE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_UPDATE_SUCCESS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.config.TenantProperties;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppRespDto;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RoleSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantTransfersVO;
import cn.iocoder.yudao.module.system.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.system.convert.premission.RoleConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.system.dal.redis.common.UserLockRedisDAO;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import cn.iocoder.yudao.module.system.mq.producer.tenant.TenantInfoUpdateProducer;
import cn.iocoder.yudao.module.system.mq.producer.tenant.TenantParamConfigProducer;
import cn.iocoder.yudao.module.system.service.permission.MenuService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import cn.iocoder.yudao.module.system.service.tenant.handler.TenantInfoHandler;
import cn.iocoder.yudao.module.system.service.tenant.handler.TenantMenuHandler;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.eventbus.rocketmq.autoconfigure.EventBusRocketMQProperties;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateEvent;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigDto;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigEvent;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TenantServiceImpl implements TenantService {

    @SuppressWarnings("SpringJavaAutowiredFieldsWarningInspection")
    @Autowired(required = false) // 由于 yudao.tenant.enable 配置项，可以关闭多门店的功能，所以这里只能不强制注入
    private TenantProperties tenantProperties;

    @Resource
    private TenantMapper tenantMapper;
    @Resource
    private TenantPackageService tenantPackageService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private AdminUserService userService;
    @Resource
    private RoleService roleService;
    @Resource
    private MenuService menuService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private TenantUserRelationService tenantUserRelationService;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantPackageRelationService tenantPackageRelationService;
    @Resource
    private ConfigApi configApi;
    @Resource
    private TenantCertificateService tenantCertificateService;
    @Resource
    private TenantParamConfigProducer tenantParamConfigProducer;

    @Resource
    private TenantInfoUpdateProducer tenantInfoUpdateProducer;

    @Resource
    private TenantThirdAppApi tenantThirdAppApi;

    @Resource
    private TransmissionOrganApi transmissionOrganApi;

    private TenantServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public List<Long> getTenantIdList() {
        List<TenantDO> tenants = tenantMapper.selectList();
        return CollectionUtils.convertList(tenants, TenantDO::getId);
    }

    @Override
    public TenantDO validTenant(Long id) {
        TenantDO tenant = getTenant(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        return tenant;
    }

    @Override
    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    @LogRecord(type = SYSTEM_TENANT_TYPE, subType = SYSTEM_TENANT_CREATE_SUB_TYPE, bizNo = "{{#tenant.id}}",
        success = SYSTEM_TENANT_CREATE_SUCCESS)
    public Long createTenant(TenantSaveReqVO createReqVO) {
        // 校验门店名称是否重复
        validateTenantForCreateOrUpdate(createReqVO);
        // 校验门店类型
        validateTenantTypeForCreate(createReqVO);
        // 创建门店
        TenantDO tenant = TenantConvert.INSTANCE.convertInitDo(createReqVO);
        tenantMapper.insert(tenant);
        // 创建证件信息
        tenantCertificateService.createOrUpdateTenantCertificates(createReqVO.getCertificates().stream().peek(c -> c.setTenantId(tenant.getId())).collect(Collectors.toList()));
        // 创建门店的管理员
        TenantUtils.execute(tenant.getId(), () -> {
            // 指定角色
            Set<Long> roleIds = createTenantInitRole(createReqVO);
            // 创建用户，并分配门店管理员角色
            Long userId = createUser(roleIds, createReqVO);
            // 修改门店的管理员
            tenantMapper.updateById(new TenantDO().setId(tenant.getId()).setContactUserId(userId));
            // 构建配置信息
            saveTenantParamConfig(createReqVO.getInquiryParamConfigs());
        });

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("tenant", tenant);
        return tenant.getId();
    }

    @Resource
    private EventBusRocketMQProperties eventBusRocketMQProperties;

    /**
     * 保存门店配置信息 {@link com.xyy.saas.inquiry.drugstore.server.mq.consumer.tenant.DrugStoreParamConfigConsumer#receiveDrugStoreParamConfig(TenantParamConfigEvent)}
     *
     * @param inquiryParamConfigs
     */
    private void saveTenantParamConfig(List<TenantParamConfigDto> inquiryParamConfigs) {
        if (CollUtil.isEmpty(inquiryParamConfigs)) {
            return;
        }
        // 抛mq - drugstore消费
        List<TenantParamConfigDto> paramConfigDtos = inquiryParamConfigs.stream().peek(p -> {
            p.setTenantId(TenantContextHolder.getRequiredTenantId());
        }).toList();
        log.info("eventBusRocketMQProperties:{},active:{}", eventBusRocketMQProperties, System.getProperty("spring.profiles.active"));
        tenantParamConfigProducer.sendMessage(TenantParamConfigEvent.builder().msg(paramConfigDtos).build());
    }


    private void validateTenantForCreateOrUpdate(TenantSaveReqVO saveReqVO) {
        if (!saveReqVO.isImport()) {
            validTenantNameDuplicate(saveReqVO.getName(), saveReqVO.getId());
            validTenantBusinessLicenseNameDuplicate(saveReqVO.getBusinessLicenseName(), saveReqVO.getId());
            validTenantBusinessLicenseNumberDuplicate(saveReqVO.getBusinessLicenseNumber(), saveReqVO.getId());
        }
    }

    private void validateTenantTypeForCreate(TenantSaveReqVO saveReqVO) {
        Integer type = Optional.ofNullable(saveReqVO.getType()).orElse(TenantTypeEnum.SINGLE_STORE.getCode());

        TenantTypeEnum tenantTypeEnum = TenantTypeEnum.fromCode(type);
        if (tenantTypeEnum == null) {
            throw exception(TENANT_TYPE_UN_SUPPORT);
        }

        // 连锁门店才能关联总部
        if (tenantTypeEnum.requiredHeadquarters() && saveReqVO.getHeadTenantId() == null) {
            throw exception(TENANT_TYPE_MUST_RELATION_CHAIN, tenantTypeEnum.getDesc());
        }
        if (!tenantTypeEnum.requiredHeadquarters() && saveReqVO.getHeadTenantId() != null) {
            throw exception(TENANT_TYPE_CAN_NOT_RELATION_CHAIN, tenantTypeEnum.getDesc());
        }

        // 校验连锁总部
        if (saveReqVO.getHeadTenantId() != null) {
            TenantDO headTenantDO = tenantMapper.selectById(saveReqVO.getHeadTenantId());
            if (headTenantDO == null) {
                throw exception(TENANT_NOT_EXISTS);
            }
            if (headTenantDO.getType() != TenantTypeEnum.CHAIN_HEADQUARTERS.getCode()) {
                throw exception(TENANT_TYPE_MUST_RELATION_CHAIN, tenantTypeEnum.getDesc());
            }
        }


    }

    private Long createUser(Set<Long> roleIds, TenantSaveReqVO createReqVO) {
        // 创建用户
        Long userId = userService.createTenantUser(TenantConvert.INSTANCE.convert02(createReqVO));
        // 分配角色
        permissionService.assignUserRole(userId, roleIds);
        return userId;
    }

    private Set<Long> createTenantInitRole(TenantSaveReqVO createReqVO) {
        Set<Long> roleIds = new HashSet<>();
        roleIds.add(roleService.selectByCode(RoleCodeEnum.STORE_ADMIN.getCode()).getId());
        if (CommonStatusEnum.isEnable(createReqVO.getWzBizTypeStatus())) {
            initWzDefaultRoles(); // 初始化问诊默认角色
        }
        return roleIds;

        // 1.创建一个默认 门店系统管理员角色 关联默认角色
//        RoleSaveReqVO reqVO = new RoleSaveReqVO();
//        reqVO.setName(RoleCodeEnum.STORE_ADMIN.getName()).setCode(RoleCodeEnum.STORE_ADMIN.getCode())
//                .setSort(0).setRemark("系统自动生成");
//        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
//        Set<Long> menus = new HashSet<>();
//        // 2.将门店创建人 关联到 系统共享角色上(目的:可随系统菜单增减)
//        roleIds.add(roleService.selectBySystemCode(RoleCodeEnum.STORE_ADMIN.getCode()).getId());
//        if(CommonStatusEnum.isEnable(createReqVO.getWzBizTypeStatus())){
//            initWzDefaultRoles(); // 初始化问诊默认角色
//            menus.addAll(TenantUtils.executeIgnore(() -> permissionService.getRoleMenuListByRoleId(NumberUtils.toLong(configApi.getConfigValueByKey(TenantConstant.TENANT_HYWZ_ADMIN_ROLE_ID)))));
//        }
//        if(CommonStatusEnum.isEnable(createReqVO.getZhlBizTypeStatus())){
//            roleIds.add(roleService.selectBySystemCode(RoleCodeEnum.ZHL_ADMIN.getCode()).getId());
//            menus.addAll(TenantUtils.executeIgnore(() -> permissionService.getRoleMenuListByRoleId(NumberUtils.toLong(configApi.getConfigValueByKey(TenantConstant.TENANT_ZHL_ADMIN_ROLE_ID)))));
//        }
//        // 分配权限
//        permissionService.assignRoleMenu(roleId, menus);
//
//        return roleIds;
    }

    /**
     * 初始化问诊默认角色
     */
    private void initWzDefaultRoles() {
        List<RoleSaveReqVO> roleSaveReqVOS = RoleConvert.INSTANCE.convertRoleEnum(RoleCodeEnum.listWzCadRole());
        roleService.initTenantSystemRoles(roleSaveReqVOS);
    }

//    private Long createRole(TenantPackageDO tenantPackage) {
//        // 创建角色
//        RoleSaveReqVO reqVO = new RoleSaveReqVO();
//        reqVO.setName(RoleCodeEnum.TENANT_ADMIN.getName()).setCode(RoleCodeEnum.TENANT_ADMIN.getCode())
//                .setSort(0).setRemark("系统自动生成");
//        Long roleId = roleService.createRole(reqVO, RoleTypeEnum.SYSTEM.getType());
//        // 分配权限
//        permissionService.assignRoleMenu(roleId, tenantPackage.getMenuIds());
//        return roleId;
//    }

    @Override
    public Long updateTenant(TenantSaveReqVO updateReqVO) {
        TenantDO tenantDO = getSelf().updateTenantInfo(updateReqVO); // 返回原始参数

        // 处理配置信息MQ +   门店信息变更MQ
        TenantUtils.execute(tenantDO.getId(), () -> {
            saveTenantParamConfig(updateReqVO.getInquiryParamConfigs());
            tenantInfoUpdateProducer.sendMessage(TenantInfoUpdateEvent.builder().msg(TenantConvert.INSTANCE.convertUpdateInfo(tenantDO)).build(), LocalDateTime.now().plusSeconds(2));
        });
        return tenantDO.getId();
    }


    @DSTransactional // 多数据源，使用 @DSTransactional 保证本地事务，以及数据源的切换
    @LogRecord(type = SYSTEM_TENANT_TYPE, subType = SYSTEM_TENANT_UPDATE_SUB_TYPE, bizNo = "{{#tenant.id}}",
        success = SYSTEM_TENANT_UPDATE_SUCCESS)
    public TenantDO updateTenantInfo(TenantSaveReqVO updateReqVO) {
        // 校验存在
        TenantDO tenant = validateUpdateTenant(updateReqVO.getId());

        // 校验门店是否重复
        validateTenantForCreateOrUpdate(updateReqVO);

        // 更新门店
        TenantDO updateObj = BeanUtils.toBean(updateReqVO, TenantDO.class);

        // 校验禁用和变更类型逻辑
        validateTenantTypeStatus(tenant, updateObj);

        tenantMapper.updateById(updateObj);
        // 设置门店总部信息
        tenantMapper.update(new UpdateWrapper<TenantDO>().set("head_tenant_id", updateReqVO.getHeadTenantId()).eq("id", updateObj.getId()));

        // 更新管理员信息
        // userService.updateUserStore(TenantConvert.INSTANCE.convertUser(updateReqVO));

        // 更新证件信息
        tenantCertificateService.createOrUpdateTenantCertificates(updateReqVO.getCertificates().stream().peek(c -> c.setTenantId(tenant.getId())).collect(Collectors.toList()));
        // 如果系统业务线 发生变化，则修改其角色的权限 1.如果权限都开启,则取并集处理菜单
        if (updateReqVO.getWzBizTypeStatus() != null || updateReqVO.getZhlBizTypeStatus() != null) {
            if (ObjectUtil.notEqual(tenant.getWzBizTypeStatus(), updateReqVO.getWzBizTypeStatus())
                || ObjectUtil.notEqual(tenant.getZhlBizTypeStatus(), updateReqVO.getZhlBizTypeStatus())) {
                Set<Long> menus = new HashSet<>();
                // 2.将门店创建人 关联到 系统共享角色上(目的:可随系统菜单增减)
                Set<Long> roleIds = new HashSet<>();
                roleIds.add(roleService.selectByCode(RoleCodeEnum.STORE_ADMIN.getCode()).getId());
                if (CommonStatusEnum.isEnable(updateReqVO.getWzBizTypeStatus())) {
                    initWzDefaultRoles(); // 初始化问诊默认角色
//                menus.addAll(permissionService.getRoleMenuListByRoleId(NumberUtils.toLong(configApi.getConfigValueByKey(TenantConstant.TENANT_HYWZ_ADMIN_ROLE_ID))));
                }
//            if(CommonStatusEnum.isEnable(updateReqVO.getZhlBizTypeStatus())){
//                menus.addAll(permissionService.getRoleMenuListByRoleId(NumberUtils.toLong(configApi.getConfigValueByKey(TenantConstant.TENANT_ZHL_ADMIN_ROLE_ID))));
//            }
//            updateTenantRoleMenu(tenant.getId(), menus);
                // 分配角色
                permissionService.assignUserRole(tenant.getContactUserId(), roleIds);
            }
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(tenant, TenantSaveReqVO.class));
        LogRecordContext.putVariable("tenant", tenant);

        return tenant;
    }

    /**
     * 校验门店变更类型和禁用
     *
     * @param tenant    数据库
     * @param updateObj 修改后的数据
     */
    private void validateTenantTypeStatus(TenantDO tenant, TenantDO updateObj) {
        // 连锁总部类型不可修改
        if (Objects.equals(tenant.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())
            && !Objects.equals(updateObj.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())) {
            throw exception(TENANT_HEAD_TYPE_CHANGE_ERROR);
        }
        // 连锁门店不可 变更为连锁总部
        if (Objects.equals(tenant.getType(), TenantTypeEnum.CHAIN_STORE.getCode())
            && Objects.equals(updateObj.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())) {
            throw exception(TENANT_HEAD_STORE_TYPE_CHANGE_ERROR);
        }
        // 总部禁用，校验存在门店
        if (Objects.equals(tenant.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())
            && CommonStatusEnum.isDisable(updateObj.getStatus())
            && CommonStatusEnum.isEnable(tenant.getStatus())) {
            if (tenantMapper.selectCount(TenantDO::getHeadTenantId, tenant.getId()) > 0) {
                throw exception(TENANT_HEAD_DISABLE_ERROR);
            }
        }
    }

    private void validTenantNameDuplicate(String name, Long id) {
        if (StringUtils.isBlank(name)) {
            return;
        }
        TenantDO tenant = tenantMapper.selectByName(name);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的门店
        if (id == null) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_NAME_DUPLICATE, name);
        }
    }

    private void validTenantBusinessLicenseNameDuplicate(String businessLicenseName, Long id) {
        if (StringUtils.isBlank(businessLicenseName)) {
            return;
        }
        TenantDO tenant = tenantMapper.selectByBusinessLicenseName(businessLicenseName);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同营业执照名的门店
        if (id == null) {
            throw exception(TENANT_BUSINESS_LICENSE_NAME_DUPLICATE, businessLicenseName);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_BUSINESS_LICENSE_NAME_DUPLICATE, businessLicenseName);
        }
    }

    private void validTenantBusinessLicenseNumberDuplicate(String businessLicenseNumber, Long id) {
        if (StringUtils.isBlank(businessLicenseNumber)) {
            return;
        }
        TenantDO tenant = tenantMapper.selectByBusinessLicenseNumber(businessLicenseNumber);
        if (tenant == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同营业执照号的门店
        if (id == null) {
            throw exception(TENANT_BUSINESS_LICENSE_NUMBER_DUPLICATE, businessLicenseNumber);
        }
        if (!tenant.getId().equals(id)) {
            throw exception(TENANT_BUSINESS_LICENSE_NUMBER_DUPLICATE, businessLicenseNumber);
        }
    }


    @Override
    @DSTransactional
    public void updateTenantRoleMenu(Long tenantId, Set<Long> menuIds) {
        TenantUtils.execute(tenantId, () -> {
            // 获得所有角色
            List<RoleDO> roles = roleService.getRoleList();
            roles.forEach(role -> Assert.isTrue(tenantId.equals(role.getTenantId()), "角色({}/{}) 门店不匹配",
                role.getId(), role.getTenantId(), tenantId)); // 兜底校验
            // 重新分配每个角色的权限
            roles.forEach(role -> {
                // 如果是门店管理员，重新分配其权限为门店套餐的权限
                if (RoleCodeEnum.isStoreAdmin(role.getCode())) {
                    permissionService.assignRoleMenu(role.getId(), menuIds);
                    log.info("[updateTenantRoleMenu][门店管理员({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), menuIds);
                    return;
                }
                // 如果是其他角色，则去掉超过套餐的权限
                Set<Long> roleMenuIds = permissionService.getRoleMenuListByRoleId(role.getId());
                roleMenuIds = CollUtil.intersectionDistinct(roleMenuIds, menuIds);
                permissionService.assignRoleMenu(role.getId(), roleMenuIds);
                log.info("[updateTenantRoleMenu][角色({}/{}) 的权限修改为({})]", role.getId(), role.getTenantId(), roleMenuIds);
            });
        });
    }

    @Override
    @LogRecord(type = SYSTEM_TENANT_TYPE, subType = SYSTEM_TENANT_DELETE_SUB_TYPE, bizNo = "{{#id}}",
        success = SYSTEM_TENANT_DELETE_SUCCESS)
    public void deleteTenant(Long id) {
        // 校验存在
        TenantDO tenantDO = validateUpdateTenant(id);
        // 校验门店类型，连锁总部需要校验下属门店
        if (tenantDO.getType() == TenantTypeEnum.CHAIN_HEADQUARTERS.getCode()) {
            boolean exists = tenantMapper.exists(new LambdaQueryWrapperX<TenantDO>().eq(TenantDO::getHeadTenantId, tenantDO.getId()));
            if (exists) {
                throw exception(TENANT_TYPE_CHAIN_STORE_CAN_NOT_DELETE);
            }
        }

        // 删除
        tenantMapper.deleteById(id);
        // 记录操作日志上下文
        LogRecordContext.putVariable("tenant", tenantDO);
    }

    private TenantDO validateUpdateTenant(Long id) {
        TenantDO tenant = tenantMapper.selectById(id);
        if (tenant == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        // 内置门店，不允许删除
        if (isSystemTenant(tenant)) {
            throw exception(TENANT_CAN_NOT_UPDATE_SYSTEM);
        }
        return tenant;
    }

    @Override
    public TenantDO getTenant(Long id) {
        return tenantMapper.selectById(id);
    }

    @Override
    public TenantDO getRequiredTenant(Long tenantId) {
        final TenantDO tenantDO = getTenant(tenantId);
        if (tenantDO == null) {
            throw exception(TENANT_NOT_EXISTS);
        }
        return tenantDO;
    }

    @Override
    public PageResult<TenantRespVO> getTenantPage(TenantPageReqVO pageReqVO) {

        // 连锁总部药师 查连锁门店
        if (permissionService.hasAnyRoles(getLoginUserId(), RoleCodeEnum.PHARMACIST.getCode())
            && Objects.equals(getTenant(TenantContextHolder.getRequiredTenantId()).getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())) {
            pageReqVO.setHeadTenantId(TenantContextHolder.getRequiredTenantId());
        }

        IPage<TenantDO> pageResult = tenantMapper.getTenantPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (pageResult == null || CollUtil.isEmpty(pageResult.getRecords())) {
            return PageResult.empty();
        }

        List<TenantRespVO> tenantRespVOS = TenantConvert.INSTANCE.convertDo2Vos(pageResult.getRecords());

        // 填充总部门店信息
        Set<Long> headTenantIds = pageResult.getRecords().stream().filter(t -> Objects.equals(t.getType(), TenantTypeEnum.CHAIN_STORE.getCode())
            && t.getHeadTenantId() != null).map(TenantDO::getHeadTenantId).collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(headTenantIds)) {
            Map<Long, TenantDO> headTenantMap = tenantMapper.selectBatchIds(headTenantIds).stream().collect(Collectors.toMap(TenantDO::getId, Function.identity(), (a, b) -> b));
            tenantRespVOS.forEach(t -> t.setHeadTenantName(headTenantMap.getOrDefault(t.getHeadTenantId(), new TenantDO()).getName()));
        }

        // 填充门店开通的三方渠道
        this.buildOrganName(tenantRespVOS);

        return new PageResult<>(tenantRespVOS, pageResult.getTotal());
    }

    /**
     * 填充门店开通的三方渠道
     *
     * @param tenantRespVOS
     */
    private void buildOrganName(List<TenantRespVO> tenantRespVOS) {

        if (CollUtil.isEmpty(tenantRespVOS)) {
            return;
        }

        List<Long> tenantIds = tenantRespVOS.stream().map(TenantRespVO::getId).toList();

        // 获取租户开通的三方渠道名称
        Map<Long, List<String>> tenantOrganNameMap = this.getTenantOrganNameMap(tenantIds);

        if (MapUtils.isEmpty(tenantOrganNameMap)) {
            return;
        }

        tenantRespVOS.forEach(item -> item.setTransmissionOrganNameList(tenantOrganNameMap.get(item.getId())));
    }

    /**
     * 获取租户开通的三方渠道名称
     *
     * @param tenantIds
     * @return
     */
    private Map<Long, List<String>> getTenantOrganNameMap(List<Long> tenantIds) {

        try {
            // 查询租户开通的渠道
            List<TenantThirdAppRespDto> tenantThirdAppRespDtoList = tenantThirdAppApi.getByTenantIdList(tenantIds);

            Set<Integer> transmissionOrganIdSet = new HashSet<>();
            Map<Long, List<Integer>> transmissionOrganIdMap = new HashMap<>();

            for (TenantThirdAppRespDto item : tenantThirdAppRespDtoList) {

                if (item.getTenantId() == null || !CommonStatusEnum.ENABLE.getStatus().equals(item.getStatus())) {
                    continue;
                }

                transmissionOrganIdSet.add(item.getTransmissionOrganId());

                if (transmissionOrganIdMap.containsKey(item.getTenantId())) {
                    transmissionOrganIdMap.get(item.getTenantId()).add(item.getTransmissionOrganId());
                } else {
                    transmissionOrganIdMap.put(item.getTenantId(), Lists.newArrayList(item.getTransmissionOrganId()));
                }
            }

            if (CollUtil.isEmpty(transmissionOrganIdSet)) {
                return null;
            }

            List<TransmissionOrganDTO> transmissionOrgans = transmissionOrganApi.getTransmissionOrgans(new ArrayList<>(transmissionOrganIdSet));

            if (CollUtil.isEmpty(transmissionOrgans)) {
                return null;
            }

            Map<Integer, String> transmissionOrganNameMap = transmissionOrgans.stream()
                .collect(Collectors.toMap(TransmissionOrganDTO::getId, TransmissionOrganDTO::getName, (v1, v2) -> v2));

            Map<Long, List<String>> tenantOrganNameMap = new HashMap<>();

            for (Entry<Long, List<Integer>> mapItem : transmissionOrganIdMap.entrySet()) {
                List<String> tenantOrganNameList = mapItem.getValue().stream().filter(transmissionOrganNameMap::containsKey).map(transmissionOrganNameMap::get).distinct().toList();
                tenantOrganNameMap.put(mapItem.getKey(), tenantOrganNameList);
            }

            return tenantOrganNameMap;
        } catch (Exception e) {
            log.error("获取租户开通的三方渠道名称异常", e);
            return null;
        }
    }

    @Override
    public TenantDO getTenantByName(String name) {
        return tenantMapper.selectByName(name);
    }

    @Override
    public List<TenantDO> getTenantByAdminUserId(Long userId) {
        return tenantMapper.selectList(TenantDO::getContactUserId, userId);
    }

    @Override
    public void handleTenantInfo(TenantInfoHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得门店
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        // 执行处理器
        handler.handle(tenant);
    }

    @Override
    public void handleTenantMenu(TenantMenuHandler handler) {
        // 如果禁用，则不执行逻辑
        if (isTenantDisable()) {
            return;
        }
        // 获得门店，然后获得菜单
        TenantDO tenant = getTenant(TenantContextHolder.getRequiredTenantId());
        Set<Long> menuIds = new HashSet<>();
        if (isSystemTenant(tenant)) { // 系统门店，菜单是全量的
            menuIds = CollectionUtils.convertSet(menuService.getMenuList(), MenuDO::getId);
        } else {
            // 1. 根据门店开通套餐包 关系,找到所有菜单集合
            List<Long> packageIds = tenantPackageRelationService.getTenantPackageRelationList(tenant.getId()).stream().map(TenantPackageRelationDO::getPackageId).distinct().collect(Collectors.toList());
            List<TenantPackageDO> tenantPackageDOS = tenantPackageService.getTenantPackageDoByCondition(TenantPackageReqDto.builder().packageIds(packageIds).build());
            menuIds = tenantPackageDOS.stream().filter(t -> CollUtil.isNotEmpty(t.getMenuIds())).flatMap(t -> t.getMenuIds().stream()).distinct().collect(Collectors.toSet());
            // 2. 获取当门店角色对应菜单
            Set<Long> roleIds = permissionService.getUserRoleIdListByUserId(getLoginUserId());
            if (CollUtil.isNotEmpty(roleIds)) {
                List<RoleDO> roles = TenantUtils.executeIgnore(() -> roleService.getRoleList(roleIds));
                roles.removeIf(role -> !CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())); // 移除禁用的角色
                menuIds.addAll(permissionService.getRoleMenuListByRoleId(convertSet(roles, RoleDO::getId)));
            }
//            menuIds = tenantPackageService.getTenantPackage(tenant.getPackageId()).getMenuIds();
        }
        // 执行处理器
        handler.handle(menuIds);
    }

    @Override
    @TenantIgnore
    public List<TenantSimpleRespVO> getTenantListByUserId(Long userId) {
        List<TenantUserRelationDO> tenantListByUserId = tenantUserRelationService.getAvailableTenantListByUserId(userId);
        if (CollUtil.isEmpty(tenantListByUserId)) {
            return List.of();
        }

        List<TenantDO> tenantDOS = tenantMapper.getAvailableTenantByIds(CollectionUtils.convertList(tenantListByUserId, TenantUserRelationDO::getTenantId));
        return TenantConvert.INSTANCE.convert2SimpleRespVO(tenantDOS);
    }


    @Override
    public TenantRespVO getTenantVo(Long id) {
        TenantDO tenant = getRequiredTenant(id);
        // 填充证件信息
        List<TenantCertificateRespVO> certificates = tenantCertificateService.getTenantCertificates(tenant.getId());
        TenantRespVO respVO = TenantConvert.INSTANCE.convertDo2Vo(tenant);
        respVO.setCertificates(certificates);

        // 获取租户开通的三方渠道名称
        Map<Long, List<String>> tenantOrganNameMap = this.getTenantOrganNameMap(Lists.newArrayList(id));
        if (MapUtils.isNotEmpty(tenantOrganNameMap)) {
            respVO.setTransmissionOrganNameList(tenantOrganNameMap.get(tenant.getId()));
        }

        return respVO;
    }

    @Override
    public List<TenantDO> getTenantList(List<Long> tenantIds) {
        if (CollUtil.isEmpty(tenantIds)) {
            return List.of();
        }
        return tenantMapper.selectBatchIds(tenantIds);
    }

    @Override
    public List<TenantDO> getTenantInfo(String nameOrPref) {
        if (StringUtils.isBlank(nameOrPref)) {
            return List.of();
        }
        return tenantMapper.selectListByNameOrPref(nameOrPref);
    }

    @Override
    public List<TenantDO> getTenantListByNames(List<String> names) {
        return tenantMapper.getTenantListByNames(names);
    }

    public void validTenantAdminUserId(Long userId) {
        // 判断解绑 管理员
        List<TenantDO> tenantDOList = getTenantByAdminUserId(userId);
        String adminTenantName = tenantDOList.stream().map(TenantDO::getName).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(adminTenantName)) {
            throw exception(TENANT_ADMIN_NOT_OPERATE, adminTenantName);
        }
    }

    @Override
    @LogRecord(type = SYSTEM_TENANT_TYPE, subType = SYSTEM_TENANT_UPDATE_SUB_TYPE, bizNo = "{{#tenant.id}}",
        success = SYSTEM_TENANT_UPDATE_SUCCESS)
    public void updateTenantStatus(Long id, Integer status) {
        TenantDO tenant = getRequiredTenant(id);

        TenantDO tenantDO = TenantDO.builder().id(id).status(status).build();
        tenantMapper.updateById(tenantDO);

        // 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(tenant, TenantSaveReqVO.class));
        LogRecordContext.putVariable("updateReqVO", BeanUtils.toBean(tenantDO, TenantSaveReqVO.class));
    }


    private static boolean isSystemTenant(TenantDO tenant) {
        return Objects.equals(tenant.getId(), TenantConstant.DEFAULT_TENANT_ID);
    }

    private boolean isTenantDisable() {
        return tenantProperties == null || Boolean.FALSE.equals(tenantProperties.getEnable());
    }

}
