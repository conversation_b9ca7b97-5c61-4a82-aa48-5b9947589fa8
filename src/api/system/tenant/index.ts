import request from '@/config/axios'
import {TENANT_CERTIFICATE_TYPE_CODE} from "@/utils/constants";

export interface TenantVO {
  id: number // 门店编号
  name: string // 门店名
  pref: string // 编码
  contactUserId: number // 联系人的用户id
  contactName: string // 联系人
  contactMobile: string // 联系手机
  accountCount: number // 账号数量
  businessLicenseName: string // 营业执照名称
  businessLicenseNumber: string // 营业执照号
  status: number // 门店状态（0正常 1停用）
  province: string // 省
  provinceCode: string // 省编码
  city: string // 市
  cityCode: string // 市编码
  area: string // 区
  areaCode: string // 区编码
  address: string // 药店地址
  disable: boolean // 是否禁用
  username: string
  password: string,
  certificates:TenantCertificateVO[], // 证件信息
  inquiryParamConfigs:InquiryParamConfigVO[], // 问诊参数配置
  ext:{
    headTenantName:string //连锁门店名称
  }
}

export interface InquiryParamConfigVO {
  paramType : number // 类型
  paramValue : string // 值
}

export interface TenantCertificateVO {
  id: number // id
  tenantId: number // 门店编号
  certificateType : number // 证件类型
  certificateName : string // 证件名称
  certificateNo : string // 证件号
  certificateImgUrls : string[] // 证件url地址
  registerTime :  Date // 注册日期
  validTime : Date // 有效期
}

export interface CertificateItem {
  id:number | undefined  // id
  label:string // 名称
  certificateType:string | number  // 证件类型
  certificateName:string | undefined  // 证件名称
  certificateNo:string  // 证件类型
  certificateImgUrls:string[]  // 证件url
  registerTime :  Date  | undefined// 注册日期
  validTime : Date | undefined // 有效期
  isName : boolean
  isRegTime : boolean
  isValidTime : boolean
}



export interface TenantPageReqVO extends PageParam {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

export interface TenantExportReqVO {
  name?: string
  contactName?: string
  contactMobile?: string
  status?: number
  createTime?: Date[]
}

// 查询门店列表
export const getTenantPage = (params: TenantPageReqVO) => {
  return request.get({ url: '/system/tenant/page', params })
}

// 查询门店列表(Table)
export const getTenantTablePage = (params) => {
  return request.get({ url: '/system/tenant-transmission-service-pack-relation/page-tenant', params })
}

// 查询门店列表
export const getTenantPages = (params: TenantPageReqVO) => {
  return request.get({ url: '/kernel/drugstore/inquiry-option-config/page-tenant', params })
}



// 查询门店详情
export const getTenant = (id: number) => {
  return request.get({ url: '/system/tenant/get?id=' + id })
}

// 新增门店
export const createTenant = (data: TenantVO) => {
  return request.post({ url: '/system/tenant/create', data })
}

// 修改门店
export const updateTenant = (data: TenantVO) => {
  return request.put({ url: '/system/tenant/update', data })
}

// 删除门店
export const deleteTenant = (id: number) => {
  return request.delete({ url: '/system/tenant/delete?id=' + id })
}

// 导出门店
export const exportTenant = (params: TenantExportReqVO) => {
  return request.download({ url: '/system/tenant/export-excel', params })
}




// 构建证件信息
export  const buildCertificateItems = (tenantCertificateItems: undefined | TenantCertificateVO[]) => {
  const certItems:CertificateItem[] = [
    {
      id:undefined,
      label:TENANT_CERTIFICATE_TYPE_CODE.YYZJ.label,
      certificateType: TENANT_CERTIFICATE_TYPE_CODE.YYZJ.code,
      certificateName:undefined,
      certificateNo:'',
      certificateImgUrls: [],
      registerTime :  undefined, // 注册日期
      validTime : undefined, // 有效期
      isName : true,
      isRegTime:false,
      isValidTime : false
    }, {
      id:undefined,
      label:TENANT_CERTIFICATE_TYPE_CODE.JYXK.label,
      certificateType: TENANT_CERTIFICATE_TYPE_CODE.JYXK.code,
      certificateName:undefined,
      certificateNo:'',
      certificateImgUrls: [],
      registerTime :  undefined, // 注册日期
      validTime : undefined, // 有效期
      isName : false,
      isRegTime:true,
      isValidTime : true
    }, {
      id:undefined,
      label:TENANT_CERTIFICATE_TYPE_CODE.JYZLGL.label,
      certificateType: TENANT_CERTIFICATE_TYPE_CODE.JYZLGL.code,
      certificateName:undefined,
      certificateNo:'',
      certificateImgUrls: [],
      registerTime :  undefined, // 注册日期
      validTime : undefined, // 有效期
      isName : false,
      isRegTime:false,
      isValidTime : false
    },
    // ========== 新增三个合同协议项 ==========
    {
      id: undefined,
      label: TENANT_CERTIFICATE_TYPE_CODE.WZFWHT.label,
      certificateType: TENANT_CERTIFICATE_TYPE_CODE.WZFWHT.code,
      certificateName: undefined,
      certificateNo: '',
      certificateImgUrls: [],
      registerTime: undefined,
      validTime: undefined,
      isName: false, // 不显示证件名称字段
      isRegTime: false, // 不显示注册时间字段
      isValidTime: false // 不显示有效期字段
    },
    {
      id: undefined,
      label: TENANT_CERTIFICATE_TYPE_CODE.BAHTJBMXY.label,
      certificateType: TENANT_CERTIFICATE_TYPE_CODE.BAHTJBMXY.code,
      certificateName: undefined,
      certificateNo: '',
      certificateImgUrls: [],
      registerTime: undefined,
      validTime: undefined,
      isName: false,
      isRegTime: false,
      isValidTime: false
    },
    {
      id: undefined,
      label: TENANT_CERTIFICATE_TYPE_CODE.YCSFBAJ.label,
      certificateType: TENANT_CERTIFICATE_TYPE_CODE.YCSFBAJ.code,
      certificateName: undefined,
      certificateNo: '',
      certificateImgUrls: [],
      registerTime: undefined,
      validTime: undefined,
      isName: false,
      isRegTime: false,
      isValidTime: false
    }
  ]
  for (const item of certItems) {
    const tci: TenantCertificateVO | undefined = tenantCertificateItems?.find(tci => tci.certificateType === item.certificateType);
    if(tci){
      item.id = tci.id
      item.certificateName = tci.certificateName
      item.certificateNo = tci.certificateNo
      item.registerTime = tci.registerTime
      item.validTime = tci.validTime
      item.certificateImgUrls = tci.certificateImgUrls
    }
  }
  return  certItems;
}
