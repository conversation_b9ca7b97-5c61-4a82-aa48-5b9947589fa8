/**
 * BETable 组件类型定义
 */

// 操作按钮配置
export interface TableAction {
  /** 按钮标签 */
  label: string
  /** 操作方法名 */
  method: string
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
  /** 是否显示，可以是布尔值或函数 */
  show?: boolean | ((row: any) => boolean)
  /** 是否禁用，可以是布尔值或函数 */
  disabled?: boolean | ((row: any) => boolean)
  /** 权限标识数组 */
  perm?: string[]
  /** 图标 */
  icon?: string
  /** 按钮大小 */
  size?: 'large' | 'default' | 'small'
  /** 是否链接按钮 */
  link?: boolean
  /** 是否朴素按钮 */
  plain?: boolean
  /** 是否圆角按钮 */
  round?: boolean
  /** 是否圆形按钮 */
  circle?: boolean
  /** 是否加载状态 */
  loading?: boolean
  /** 是否自动聚焦 */
  autofocus?: boolean
  /** 原生 type 属性 */
  nativeType?: 'button' | 'submit' | 'reset'
  /** 是否自动插入空格 */
  autoInsertSpace?: boolean
  /** 颜色 */
  color?: string
  /** 暗黑模式 */
  dark?: boolean
  /** 按钮组 */
  group?: boolean
  /** 按钮组类型 */
  groupType?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info'
  /** 按钮组大小 */
  groupSize?: 'large' | 'default' | 'small'
  /** 按钮组是否禁用 */
  groupDisabled?: boolean
  /** 按钮组是否朴素 */
  groupPlain?: boolean
  /** 按钮组是否圆角 */
  groupRound?: boolean
  /** 按钮组是否圆形 */
  groupCircle?: boolean
  /** 按钮组是否加载 */
  groupLoading?: boolean
  /** 按钮组是否自动聚焦 */
  groupAutofocus?: boolean
  /** 按钮组原生类型 */
  groupNativeType?: 'button' | 'submit' | 'reset'
  /** 按钮组是否自动插入空格 */
  groupAutoInsertSpace?: boolean
  /** 按钮组颜色 */
  groupColor?: string
  /** 按钮组暗黑模式 */
  groupDark?: boolean
}

// 列配置
export interface TableColumn {
  /** 列属性名 */
  prop: string
  /** 列标签 */
  label: string
  /** 列宽度 */
  width?: string | number
  /** 最小宽度 */
  mWidth?: string | number
  /** 列对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 列类型 */
  type?: 'selection' | 'index' | 'actions'
  /** 是否固定列 */
  fixed?: boolean | 'left' | 'right'
  /** 是否隐藏 */
  hidden?: boolean
  /** 是否使用插槽 */
  slot?: boolean
  /** 格式化函数 */
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  /** 过滤器 */
  filters?: Array<{ text: string; value: any }>
  /** 过滤方法 */
  filterMethod?: (value: any, row: any, column: any) => boolean
  /** 操作按钮配置（当 type 为 'actions' 时使用） */
  actions?: TableAction[]
  /** 列键名 */
  columnKey?: string
  /** 是否显示溢出提示 */
  showOverflowTooltip?: boolean
  /** 是否可排序 */
  sortable?: boolean | 'custom'
  /** 排序方法 */
  sortMethod?: (a: any, b: any) => number
  /** 排序方向 */
  sortBy?: string | string[] | ((row: any, index: number) => string | number)
  /** 排序顺序 */
  sortOrders?: Array<'ascending' | 'descending' | null>
  /** 是否可调整大小 */
  resizable?: boolean
  /** 列类名 */
  className?: string
  /** 列样式 */
  style?: object
  /** 列标题类名 */
  labelClassName?: string
  /** 列标题样式 */
  labelStyle?: object
  /** 列内容类名 */
  cellClassName?: string | ((row: any, column: any, rowIndex: number, columnIndex: number) => string)
  /** 列内容样式 */
  cellStyle?: object | ((row: any, column: any, rowIndex: number, columnIndex: number) => object)
  /** 列标题渲染函数 */
  renderHeader?: (h: any, params: { column: any; $index: number }) => any
  /** 列内容渲染函数 */
  renderCell?: (h: any, params: { row: any; column: any; $index: number }) => any
  /** 列展开行 */
  expandRow?: boolean
  /** 列展开行渲染函数 */
  renderExpand?: (h: any, params: { row: any; column: any; $index: number }) => any
  /** 列展开行类名 */
  expandRowClassName?: string | ((row: any, rowIndex: number) => string)
  /** 列展开行样式 */
  expandRowStyle?: object | ((row: any, rowIndex: number) => object)
  /** 列展开行是否显示 */
  expandRowShow?: boolean | ((row: any, rowIndex: number) => boolean)
  /** 列展开行是否禁用 */
  expandRowDisabled?: boolean | ((row: any, rowIndex: number) => boolean)
  /** 列展开行是否加载 */
  expandRowLoading?: boolean | ((row: any, rowIndex: number) => boolean)
  /** 列展开行是否自动聚焦 */
  expandRowAutofocus?: boolean | ((row: any, rowIndex: number) => boolean)
  /** 列展开行原生类型 */
  expandRowNativeType?: 'button' | 'submit' | 'reset' | ((row: any, rowIndex: number) => 'button' | 'submit' | 'reset')
  /** 列展开行是否自动插入空格 */
  expandRowAutoInsertSpace?: boolean | ((row: any, rowIndex: number) => boolean)
  /** 列展开行颜色 */
  expandRowColor?: string | ((row: any, rowIndex: number) => string)
  /** 列展开行暗黑模式 */
  expandRowDark?: boolean | ((row: any, rowIndex: number) => boolean)
}

// 表格事件
export interface TableActionEvent {
  /** 操作方法名 */
  method: string
  /** 行数据 */
  row: any
}

// 表格属性
export interface BETableProps {
  /** 表格数据 */
  tableData: any[]
  /** 列配置 */
  columns: TableColumn[]
  /** 加载状态 */
  loading?: boolean
  /** 按钮样式类 */
  btnClass?: boolean
  /** 是否显示选择框 */
  showSelection?: boolean
  /** 是否显示序号列 */
  showIndex?: boolean
  /** 是否显示边框 */
  borders?: boolean
  /** 权限标识 */
  hasPermi?: string
  /** 本地存储键名 */
  keyLocalStorage?: string
}

// 表格事件
export interface BETableEmits {
  /** 操作事件 */
  action: [event: TableActionEvent]
  /** 行点击事件 */
  'row-click': [row: any]
  /** 选择变化事件 */
  'selection-change': [selection: any[]]
  /** 过滤变化事件 */
  'filter-change': [filters: any]
  /** 列顺序变化事件 */
  'column-order-change': [columns: TableColumn[]]
}

// 权限检查结果
export interface PermissionResult {
  /** 是否有权限 */
  hasPermission: boolean
  /** 权限标识 */
  permissions: string[]
  /** 错误信息 */
  error?: string
}

// 权限检查函数类型
export type PermissionChecker = (permissions: string[]) => PermissionResult 