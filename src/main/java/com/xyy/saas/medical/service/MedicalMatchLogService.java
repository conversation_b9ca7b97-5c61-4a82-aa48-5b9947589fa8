package com.xyy.saas.medical.service;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.medical.po.MedicalMatchLogPo;
import org.springframework.stereotype.Service;

/**
 * @Auther: chen1
 * @Date: 2019/9/9 11:23
 * @Description:
 */
public interface MedicalMatchLogService {


    PageInfo queryMatchLogList(PageInfo pageInfo);

    PageInfo queryMatchLogsByTime(String organSign,String startTime, String endTime, PageInfo pageInfo);

    void insertMatchLog(MedicalMatchLogPo medicalMatchLogPo);
}
