<template>
  <div>
    <el-form :model="formData" label-width="180px">
      <el-form-item label="执行标准" prop="executionStandard">
        <div class="input-group">
          <el-input v-model="formData.executionStandard" placeholder="" maxlength="20" clearable :disabled="apprType == 'audit' || apprType == 'look'" />
          <UploadImgs
            v-model="formData.executionStandardExtUrls"
            :limit="4"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'executionStandard')"
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
            :disabled="apprType == 'audit' || apprType == 'look'"
          />
        </div>
      </el-form-item>
      <el-form-item label="质量标准" prop="qualityStandard">
        <div class="input-group">
          <el-input v-model="formData.qualityStandard" :disabled="apprType == 'audit' || apprType == 'look'" placeholder="" maxlength="20" clearable />
          <UploadImgs
            v-model="formData.qualityStandardExtUrls"
            :limit="4"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'qualityStandard')"
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            :disabled="apprType == 'audit' || apprType == 'look'"
            class="min-w-180px uploadImgs"
          />
        </div>
      </el-form-item>
      <el-form-item label="药品注册证" prop="drugRegistrationCertificate">
        <div class="input-group">
          <el-input
            v-model="formData.drugRegistrationCertificate"
            placeholder=""
            maxlength="20"
            :disabled="apprType == 'audit' || apprType == 'look'"
            clearable
          />
          <el-date-picker
            v-model="formData.drugRegistrationCertificateEnd"
            type="date"
            placeholder="选择有效期"
            style="margin-left: 10px"
            :disabled="apprType == 'audit' || apprType == 'look'"
          />
          <UploadImgs
            v-model="formData.drugRegistrationCertificateExtUrls"
            :limit="4"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'drugRegistrationCertificate')"
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            :disabled="apprType == 'audit' || apprType == 'look'"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="药品再注册证" prop="drugReRegistrationCertificate">
        <div class="input-group">
          <el-input
            v-model="formData.drugReRegistrationCertificate"
            placeholder=""
            maxlength="20"
            :disabled="apprType == 'audit' || apprType == 'look'"
            clearable
          />
          <el-date-picker
            v-model="formData.drugReRegistrationCertificateEnd"
            type="date"
            placeholder="选择有效期"
            :disabled="apprType == 'audit' || apprType == 'look'"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.drugReRegistrationCertificateExtUrls"
            :limit="4"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'drugReRegistrationCertificate')"
            height="30px"
            width="30px"
            :disabled="apprType == 'audit' || apprType == 'look'"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="药品补充申请批件" prop="drugSupplementaryApplicationBatchCertificate">
        <div class="input-group">
          <el-input
          :disabled="apprType == 'audit' || apprType == 'look'"
            v-model="formData.drugSupplementaryApplicationBatchCertificate"
            placeholder=""
            maxlength="20"
            clearable
          />
          <el-date-picker
            v-model="formData.drugSupplementaryApplicationBatchCertificateEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.drugSupplementaryApplicationBatchCertificateExtUrls"
            :limit="4"
            :before-upload="beforeUpload"
            :disabled="apprType == 'audit' || apprType == 'look'"
            :on-success="
              (res) => handleUploadSuccess(res, 'drugSupplementaryApplicationBatchCertificate')
            "
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="医药产品注册证" prop="medicinalProductRegistrationCertificate">
        <div class="input-group">
          <el-input
            v-model="formData.medicinalProductRegistrationCertificate"
            placeholder=""
            maxlength="20"
            :disabled="apprType == 'audit' || apprType == 'look'"
            clearable
          />
          <el-date-picker
            v-model="formData.medicinalProductRegistrationCertificateEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.medicinalProductRegistrationCertificateExtUrls"
            :limit="4"
            :before-upload="beforeUpload"
            :disabled="apprType == 'audit' || apprType == 'look'"
            :on-success="
              (res) => handleUploadSuccess(res, 'medicinalProductRegistrationCertificate')
            "
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="进口药品注册证" prop="importedDrugRegistrationCertificate">
        <div class="input-group">
          <el-input
          :disabled="apprType == 'audit' || apprType == 'look'"
            v-model="formData.importedDrugRegistrationCertificate"
            placeholder=""
            maxlength="20"
            clearable
          />
          <el-date-picker
            v-model="formData.importedDrugRegistrationCertificateEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.importedDrugRegistrationCertificateExtUrls"
            :limit="1"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'importedDrugRegistrationCertificate')"
            height="30px"
            width="30px"
            :disabled="apprType == 'audit' || apprType == 'look'"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item
        label="进口药品补充申请批件"
        prop="importedDrugSupplementaryApplicationBatchCertificate"
      >
        <div class="input-group">
          <el-input
          :disabled="apprType == 'audit' || apprType == 'look'"
            v-model="formData.importedDrugSupplementaryApplicationBatchCertificate"
            placeholder=""
            maxlength="20"
            clearable
          />
          <el-date-picker
            v-model="formData.importedDrugSupplementaryApplicationBatchCertificateEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.importedDrugSupplementaryApplicationBatchCertificateExtUrls"
            :limit="1"
            :before-upload="beforeUpload"
            :disabled="apprType == 'audit' || apprType == 'look'"
            :on-success="
              (res) =>
                handleUploadSuccess(res, 'importedDrugSupplementaryApplicationBatchCertificate')
            "
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="进口药品再注册" prop="importedDrugReRegistrationCertificate">
        <div class="input-group">
          <el-input
          :disabled="apprType == 'audit' || apprType == 'look'"
            v-model="formData.importedDrugReRegistrationCertificate"
            placeholder=""
            maxlength="20"
            clearable
          />
          <el-date-picker
            v-model="formData.importedDrugReRegistrationCertificateEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.importedDrugReRegistrationCertificateExtUrls"
            :limit="1"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'importedDrugReRegistrationCertificate')"
            height="30px"
            width="30px"
            :disabled="apprType == 'audit' || apprType == 'look'"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="药品注册批件" prop="drugRegistrationBatchCertificate">
        <div class="input-group">
          <el-input
          :disabled="apprType == 'audit' || apprType == 'look'"
            v-model="formData.drugRegistrationBatchCertificate"
            placeholder=""
            maxlength="20"
            clearable
          />
          <el-date-picker
            v-model="formData.drugRegistrationBatchCertificateEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.drugRegistrationBatchCertificateExtUrls"
            :limit="1"
            :disabled="apprType == 'audit' || apprType == 'look'"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'drugRegistrationBatchCertificate')"
            height="30px"
            width="30px"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
      <el-form-item label="样盒-说明书" prop="sampleBoxInstructions">
        <div class="input-group">
          <el-input
            v-model="formData.sampleBoxInstructions"
            placeholder=""
            :disabled="apprType == 'audit' || apprType == 'look'"
            maxlength="20"
            clearable
          />
          <el-date-picker
            v-model="formData.sampleBoxInstructionsEnd"
            type="date"
            :disabled="apprType == 'audit' || apprType == 'look'"
            placeholder="选择有效期"
            style="margin-left: 10px"
          />
          <UploadImgs
            v-model="formData.sampleBoxInstructionsExtUrls"
            :limit="1"
            :before-upload="beforeUpload"
            :on-success="(res) => handleUploadSuccess(res, 'sampleBoxInstructions')"
            height="30px"
            width="30px"
            :disabled="apprType == 'audit' || apprType == 'look'"
            float="none"
            style="margin-left: 10px"
            class="min-w-180px"
          />
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
const message = useMessage()

const props = defineProps({
  qualificationInfo: {
    type: Object,
    default: () => ({})
  },
  apprType:  {
    type: String,
    default: ''
  },
})

const formData = ref({
  qualityStandard: '',
  qualityStandardExtUrls: [],
  executionStandard: '',
  executionStandardExtUrls: [],
  drugRegistrationCertificate: '',
  drugRegistrationCertificateExtUrls: [],
  drugRegistrationCertificateEnd: null,
  drugReRegistrationCertificate: '',
  drugReRegistrationCertificateExtUrls: [],
  drugReRegistrationCertificateEnd: null,
  drugSupplementaryApplicationBatchCertificate: '',
  drugSupplementaryApplicationBatchCertificateExtUrls: [],
  drugSupplementaryApplicationBatchCertificateEnd: null,
  medicinalProductRegistrationCertificate: '',
  medicinalProductRegistrationCertificateExtUrls: [],
  medicinalProductRegistrationCertificateEnd: null,
  importedDrugRegistrationCertificate: '',
  importedDrugRegistrationCertificateExtUrls: [],
  importedDrugRegistrationCertificateEnd: null,
  importedDrugSupplementaryApplicationBatchCertificate: '',
  importedDrugSupplementaryApplicationBatchCertificateExtUrls: [],
  importedDrugSupplementaryApplicationBatchCertificateEnd: null,
  gmpCertificate: '',
  gmpCertificateExtUrls: [],
  gmpCertificateStartEnd: [],
  importedDrugReRegistrationCertificate: '',
  importedDrugReRegistrationCertificateExtUrls: [],
  importedDrugReRegistrationCertificateEnd: null,
  drugRegistrationBatchCertificate: '',
  drugRegistrationBatchCertificateExtUrls: [],
  drugRegistrationBatchCertificateEnd: null,
  sampleBoxInstructions: '',
  sampleBoxInstructionsEnd: null,
  sampleBoxInstructionsExtUrls: []
})
watch(
  () => props.qualificationInfo,
  (newVal) => {
    if (newVal) {
      formData.value = {
        ...formData.value,
        ...newVal
      }
    }
  },
  { immediate: true, deep: true }
)
/** 文件上传相关方法 */
const beforeUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  const isLt1M = file.size / 1024 / 1024 < 1

  if (!isJPG && !isPNG) {
    message.error('上传图片只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt1M) {
    message.error('上传图片大小不能超过 1MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, field: string) => {
  formData.value[field + 'Ext'].urls = [response.url]
  message.success('上传成功')
}
const getQualificationInfo = () => {
  // 只保留 ProductQualificationInfoVO 相关字段
  const qualificationInfoVO = {
    qualityStandard: formData.value.qualityStandard,
    qualityStandardExt: { urls: formData.value.qualityStandardExtUrls },
    executionStandard: formData.value.executionStandard,
    executionStandardExt: { urls: formData.value.executionStandardExtUrls },
    drugRegistrationCertificate: formData.value.drugRegistrationCertificate,
    drugRegistrationCertificateExt: { urls: formData.value.drugRegistrationCertificateExtUrls },
    drugRegistrationCertificateEnd: formData.value.drugRegistrationCertificateEnd,
    drugReRegistrationCertificate: formData.value.drugReRegistrationCertificate,
    drugReRegistrationCertificateExt: { urls: formData.value.drugReRegistrationCertificateExtUrls },
    drugReRegistrationCertificateEnd: formData.value.drugReRegistrationCertificateEnd,
    drugSupplementaryApplicationBatchCertificate:
      formData.value.drugSupplementaryApplicationBatchCertificate,
    drugSupplementaryApplicationBatchCertificateExt: {
      urls: formData.value.drugSupplementaryApplicationBatchCertificateExtUrls
    },
    drugSupplementaryApplicationBatchCertificateEnd:
      formData.value.drugSupplementaryApplicationBatchCertificateEnd,
    medicinalProductRegistrationCertificate: formData.value.medicinalProductRegistrationCertificate,
    medicinalProductRegistrationCertificateExt: {
      urls: formData.value.medicinalProductRegistrationCertificateExtUrls
    },
    medicinalProductRegistrationCertificateEnd:
      formData.value.medicinalProductRegistrationCertificateEnd,
    importedDrugRegistrationCertificate: formData.value.importedDrugRegistrationCertificate,
    importedDrugRegistrationCertificateExt: {
      urls: formData.value.importedDrugRegistrationCertificateExtUrls
    },
    importedDrugRegistrationCertificateEnd: formData.value.importedDrugRegistrationCertificateEnd,
    importedDrugSupplementaryApplicationBatchCertificate:
      formData.value.importedDrugSupplementaryApplicationBatchCertificate,
    importedDrugSupplementaryApplicationBatchCertificateExt: {
      urls: formData.value.importedDrugSupplementaryApplicationBatchCertificateExtUrls
    },
    importedDrugSupplementaryApplicationBatchCertificateEnd:
      formData.value.importedDrugSupplementaryApplicationBatchCertificateEnd,
    gmpCertificate: formData.value.gmpCertificate,
    gmpCertificateExt: { urls: formData.value.gmpCertificateExtUrls },
    gmpCertificateStart: formData.value.gmpCertificateStartEnd?.[0],
    gmpCertificateEnd: formData.value.gmpCertificateStartEnd?.[1],
    importedDrugReRegistrationCertificate: formData.value.importedDrugReRegistrationCertificate,
    importedDrugReRegistrationCertificateExt: {
      urls: formData.value.importedDrugReRegistrationCertificateExtUrls
    },
    importedDrugReRegistrationCertificateEnd:
      formData.value.importedDrugReRegistrationCertificateEnd,
    drugRegistrationBatchCertificate: formData.value.drugRegistrationBatchCertificate,
    drugRegistrationBatchCertificateExt: {
      urls: formData.value.drugRegistrationBatchCertificateExtUrls
    },
    drugRegistrationBatchCertificateEnd: formData.value.drugRegistrationBatchCertificateEnd,
    sampleBoxInstructionsExt: { urls: formData.value.sampleBoxInstructionsExtUrls }
  }

  return qualificationInfoVO as unknown as ProductQualificationInfoVO
}

defineExpose({
  getQualificationInfo
})
</script>
<style lang="scss" scoped>
.input-group {
  display: flex;
  gap: 12px;
  input {
    height: 32px;
  }
}
:deep(.el-date-picker) {
  width: 200px !important;  // 修改为所需宽度
  
  .el-input__wrapper {
    width: 100%;
  }
}
:deep(.uploadImgs){
  // .upload-handle{
  //   flex-direction: column;
  //   .handle-icon{
  //     flex-direction: row !important;
  //   }
  // }
}
</style>
