<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="医院名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入医院名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="医院编码" prop="pref">
        <el-input  
          v-model="queryParams.pref"
          placeholder="请输入医院编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item label="是否禁用" prop="disable">
        <el-select
          v-model="queryParams.disable"
          placeholder="请选择启禁用状态"
          clearable
          class="!w-240px"
          :default-first-option=true
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.HOSPITAL_DISABLE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hospital:inquiry-hospital:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hospital:inquiry-hospital:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column type="index" width="50"/>
      <el-table-column label="医院编码" align="center" prop="pref" min-width="120"/>
        <el-table-column label="医院机构编码" align="center" prop="institutionCode" min-width="140"/>
      <el-table-column label="医院名称" align="center" prop="name" min-width="120"/>
      <el-table-column label="医院等级" align="center" prop="level" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HOSPITAL_LEVEL" :value="scope.row.level" />
        </template>
      </el-table-column>
      <el-table-column label="医院地址" align="center" prop="address" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="电子邮件" align="center" prop="email" />
      <el-table-column label="官方网站" align="center" prop="website" />
      <el-table-column label="医保资质" align="center" prop="hasMedicare" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HOSPITAL_HAS_MEDICARE" :value="scope.row.hasMedicare" />
        </template>
      </el-table-column>
      <el-table-column label="默认处方模板" align="center" width="200px">
        <template #default="scope">
          <p >{{ scope.row.setting.defaultWesternPrescriptionTemplateName }}</p>
          <p >{{ scope.row.setting.defaultChinesePrescriptionTemplateName }}</p>
        </template>
      </el-table-column>
      <el-table-column label="是否禁用" align="center" prop="disable" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HOSPITAL_DISABLE_STATUS" :value="scope.row.disable" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hospital:inquiry-hospital:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hospital:inquiry-hospital:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InquiryHospitalForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InquiryHospitalApi, InquiryHospitalVO } from '@/api/inquiry/hospital'
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict"
import InquiryHospitalForm from './InquiryHospitalForm.vue'
import HospitalDeptForm from './inquiryhospitaldepartmentrelation/HospitalDeptForm.vue'

/** 医院信息 列表 */
defineOptions({ name: 'InquiryHospital' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<InquiryHospitalVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  id: undefined,
  name: undefined,
  pref: undefined,
  level: undefined,
  address: undefined,
  phone: undefined,
  email: undefined,
  website: undefined,
  hasMedicare: undefined,
  defaultPrescriptionTemplate: undefined,
  disable: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InquiryHospitalApi.getInquiryHospitalPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()

const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InquiryHospitalApi.deleteInquiryHospital(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InquiryHospitalApi.exportInquiryHospital(queryParams)
    download.excel(data, '医院信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
