<template>
  <div class="right-prescription-record">
    <template v-if="state.list.length">
      <div class="record-section">
        <div class="list-view">
          <template v-for="listItem in state.list" :key="listItem.id">
            <div class="list-view-item">
              <div class="head" @click="onClick2PrescriptionDetail(listItem)">
                <span class="h-txt">{{ listItem.medicineTypeStr }}</span>
                <span class="h-link"
                  >详情<img class="link-arrow" src="@/assets/inquiry/doctor/icon-arrow-right.png"
                /></span>
              </div>
              <div class="content">
                <span class="cnt-title">处方号</span>
                <span class="cnt-body">{{ listItem.pref }}</span>
              </div>
              <div class="content">
                <span class="cnt-title">诊断</span>
                <span class="cnt-body">{{ listItem.diagnosisNameStr }}</span>
              </div>
              <div class="content">
                <span class="cnt-title">开具时间</span>
                <span class="cnt-body">{{ listItem.outPrescriptionTimeFormatStr }}</span>
              </div>
            </div>
          </template>
        </div>
        <div class="pager-view">
          <el-pagination
            ref="refElPagination"
            v-model:current-page="state.pageNo"
            v-model:page-size="state.pageSize"
            :total="state.total"
            :pager-count="3"
            layout="total, jumper, prev, pager, next"
            size="small"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </template>
    <template v-else>
      <div class="record-empty">暂无可查看的内容哦~</div>
    </template>
  </div>

  <!-- 处方详情dialog -->
  <PdfPreview ref="refPdfPreview" />
  <DialogPrescriptInfo :inquiryPref="inquiryPref" v-model="showDialogPrescriptInfo" />
</template>

<script setup>
import { ref } from 'vue'
import dayjs from 'dayjs'
import PdfPreview from '@/views/inquiry/prescriptiontemplate/pdf/PdfPreview.vue' // 处方预览
import DialogPrescriptInfo from './DialogPrescriptInfo/index.vue' // 处方详情dialog
import { InquiryWorkstationApi } from '@/api/inquiry/inquiryWorkstation/index'
import { InquiryBizTypeEnum } from '@/utils/constants'

const message = useMessage() // 消息弹窗

const state = reactive({
  curInquiry: null, // 当前问诊
  pageNo: 1,
  pageSize: 20,
  total: 0,
  list: []
})

const refPdfPreview = ref(null) // 处方详情弹窗组件

const reset = () => {
  state.curInquiry = null
  state.pageNo = 1
  state.total = 0
  state.list = []
}

const init = ({ inquiry }) => {
  reset()
  state.curInquiry = inquiry
  queryPatientPrescriptionList()
}

// 查询处方列表
const queryPatientPrescriptionList = async () => {
  try {
    const resp = await InquiryWorkstationApi.getPatientPrescriptionList({
      pageNo: state.pageNo,
      pageSize: state.pageSize,
      patientPref: state.curInquiry.patientPref,
      queryScene: 0
    })

    state.total = resp.total
    if (Array.isArray(resp.list) && resp.list.length) {
      let originList = resp.list.map((item) => {
        return {
          ...item,
          medicineTypeStr: (() => {
            switch (item.medicineType) {
              case 0:
                return '西成药处方'
              case 1:
                return '中成药处方'
              default:
                return ''
            }
          })(),
          diagnosisNameStr: (() => {
            if (Array.isArray(item.diagnosisName) && item.diagnosisName.length) {
              return item.diagnosisName.join('、')
            } else {
              return ''
            }
          })(),
          outPrescriptionTimeFormatStr: dayjs(item.outPrescriptionTime).format(
            'YYYY-MM-DD HH:mm:ss'
          )
        }
      })
      // state.list = state.pageNo == 1 ? originList : [...state.list, ...originList]
      state.list = originList
      setElPaginationJumpLabel()
    }
  } catch (err) {}
}

// 分页
const handlePageChange = (pageNo) => {
  state.pageNo = pageNo
  queryPatientPrescriptionList()
}

const showDialogPrescriptInfo = ref(false)
const inquiryPref = ref('')
// 查看处方详情
const onClick2PrescriptionDetail = (prescriptionItem) => {
  // #region
  /** 
  if (prescriptionItem.inquiryBizType == InquiryBizTypeEnum.STORE.value) {
    // 药店问诊，取pdf
    if (prescriptionItem.prescriptionPdfUrl) {
      refPdfPreview.value?.show(prescriptionItem.prescriptionPdfUrl, '处方详情')
    } else {
      message.warning('暂无处方图片')
    }
  } else if (prescriptionItem.inquiryBizType == InquiryBizTypeEnum.REMOTE.value) {
    // 远程审方, 取图片
    if (prescriptionItem.prescriptionImgUrl) {
      refPdfPreview.value?.show(prescriptionItem.prescriptionImgUrl, '处方详情', 'image')
    } else {
      message.warning('暂无处方图片')
    }
  } else {
    message.warning('暂无处方图片')
  }
  */
  // #endregion

  showDialogPrescriptInfo.value = true
  inquiryPref.value = prescriptionItem.inquiryPref
}

// 设置分页器jumper的文案
const refElPagination = ref(null)
const setElPaginationJumpLabel = () => {
  nextTick(() => {
    const domElPagination = refElPagination.value.$el
    const domTotal = domElPagination.querySelector(`.el-pagination__total`)
    domTotal.innerHTML = domTotal.innerHTML.replace(/\s/g, '')
    domElPagination.querySelector(`.el-pagination__jump .el-pagination__goto`).innerHTML = '跳转'
    domElPagination.querySelector(`.el-pagination__jump .el-pagination__classifier`).innerHTML = ''
  })
}

defineExpose({
  init
})
</script>

<style lang="scss" scoped>
.right-prescription-record {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .record-empty {
    width: 100%;
    text-align: center;
    color: #666666;
    font-size: 15px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .record-section {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;

    .list-view {
      flex-grow: 1;
      width: 100%;
      overflow-x: hidden;
      overflow-y: auto;

      .list-view-item {
        width: 100%;
        padding: 12px 16px;
        position: relative;

        &::after {
          content: '';
          width: calc(100% - 16px * 2);
          height: 1px;
          background-color: #e1e5ee;
          position: absolute;
          left: 16px;
          bottom: 0;
        }

        .head {
          width: 100%;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .h-txt {
            flex-grow: 1;
            line-height: 1;
            color: #222222;
            font-size: 15px;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .h-link {
            flex-shrink: 0;
            line-height: 1;
            cursor: pointer;
            user-select: none;
            color: #666666;
            font-size: 13px;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .link-arrow {
              width: 14px;
              height: 14px;
            }
          }
        }

        .content {
          width: 100%;
          margin: 12px 0 0;
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .cnt-title {
            flex-shrink: 0;
            width: 64px;
            color: #222222;
            font-size: 14px;
          }

          .cnt-body {
            flex-grow: 1;
            color: #222222;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .pager-view {
      flex-shrink: 0;
      width: 100%;
      height: 54px;
      position: relative;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      &::before {
        content: '';
        width: 100%;
        height: 10px;
        background: linear-gradient(to bottom, transparent, #f0f2f5);
        position: absolute;
        top: -10px;
        left: 0;
      }

      .el-pagination {
        padding: 0;
        color: #666666;
        font-size: 14px;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        :deep(.el-pagination__total) {
          color: #222222;
          font-size: 12px;
          font-weight: 400;

          &.is-first {
            margin-right: 4px;
          }
        }

        :deep(.btn-prev) {
          padding-right: 0;
          margin-left: 0;
        }

        :deep(.btn-next) {
          padding-left: 0;
        }

        :deep(.el-pager) {
          /**
          li {
            &:hover {
              color: #00b955;
            }

            &.active {
              color: #00b955;
            }

          }
          */
        }

        :deep(.el-pagination__jump) {
          margin-left: 0;
          color: #222222;
          font-size: 12px;
          font-weight: 400;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .el-pagination__editor {
            width: 28px;
            height: 24px;
            line-height: 24px;
            font-size: 14px;
            color: #222222;

            .el-input__inner {
              height: 24px;

              &:focus {
                border-color: #00b955;
              }
            }
          }

          .el-pagination__classifier {
            margin-left: 0;
          }
        }

        :deep(.el-pagination__goto) {
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
