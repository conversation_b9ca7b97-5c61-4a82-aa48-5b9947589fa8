<template>

  <el-collapse v-model="activeNames" @change="handleChange">
    <!-- <el-collapse-item title="问诊需审核" :name="INQUIRY_OPTION_TYPE.PROC_IDENTITY_REQUIRED.type">
      <area-store-index :active="activeNames" :option-type="INQUIRY_OPTION_TYPE.PROC_IDENTITY_REQUIRED"
      add-tip="需要药店在问诊单页面操作页面后派单" del-tip="填写完问诊表单直接进行匹配流程"/>
    </el-collapse-item> -->
    <el-collapse-item title="患者查看处方" :name="INQUIRY_OPTION_TYPE.PROC_WE_CHAT_INQUIRY_PATIENT_VIEW_PRESCRIPTION.type">
      <area-store-index :active="activeNames" :option-type="INQUIRY_OPTION_TYPE.PROC_WE_CHAT_INQUIRY_PATIENT_VIEW_PRESCRIPTION"
                        add-tip="患者端小程序可以查看处方详情" del-tip="患者端小程序不可以查看处方详情"/>
    </el-collapse-item>


 </el-collapse>

</template>
<script setup>

import areaStoreIndex from './baseComponents/areaStoreIndex.vue'
import basicInfoSettings from './baseComponents/basicInfoSettings/index.vue'
import {INQUIRY_OPTION_TYPE} from "@/utils/constants";


const activeNames = ref([]) ;
const isAll = ref(false) ;



const handleChange = (type)=>{
}



</script>
<style scoped>

</style>
